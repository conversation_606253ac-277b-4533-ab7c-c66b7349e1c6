// ��Mn��
require('dotenv').config();

module.exports = {
  // � ѯ�ID
  CLOUD_ENV_ID: process.env.CLOUD_ENV_ID || 'cloudbase-4g6zy3zn7d0f0cd4',
  
  // pn�Mn
  DB_CONFIG: {
    host: process.env.DB_HOST,
    port: process.env.DB_PORT || 3306,
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME
  },
  
  // ~��APIƥ
  TENCENT_CLOUD: {
    secretId: process.env.SecretId,
    secretKey: process.env.SecretKey
  },
  
  // 
���
  PORT: process.env.PORT || 3000
};