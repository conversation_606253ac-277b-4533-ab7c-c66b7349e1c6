{"env": "cloud1-2gokpbk70403ca97", "setting": {"es6": true, "postcss": true, "minified": true, "uglifyFileName": false, "enhance": true, "packNpmRelationList": [{"packageJsonPath": "./package.json", "miniprogramNpmDistDir": "./"}], "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "useCompilerPlugins": false, "compileWorklet": false, "uploadWithSourceMap": true, "packNpmManually": false, "minifyWXSS": true, "minifyWXML": true, "localPlugins": false, "disableUseStrict": false, "condition": false, "swc": false, "disableSWC": true}, "compileType": "miniprogram", "simulatorPluginLibVersion": {}, "packOptions": {"ignore": [], "include": []}, "appid": "wx910771639ac9e8d7", "editorSetting": {}, "cloudfunctionRoot": "cloudfunctions/", "libVersion": "2.31.1", "cloudbaseRoot": "cloudfunctions/", "cloudbase": {"env": "cloud1-2gokpbk70403ca97"}, "cloudfunctionTemplateRoot": "cloudfunctionTemplate/"}