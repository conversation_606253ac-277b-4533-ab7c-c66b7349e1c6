const fs = require('fs');
const path = require('path');

/**
 * 获取指定目录的结构
 * @param {string} dirPath - 目录路径
 * @param {number} depth - 当前深度
 * @param {number} maxDepth - 最大深度
 * @returns {string} 目录结构的字符串表示
 */
function getDirectoryStructure(dirPath, depth = 0, maxDepth = 3) {
  if (depth > maxDepth) return '';
  
  const indent = '  '.repeat(depth);
  let structure = `${indent}└── ${path.basename(dirPath)}\n`;
  
  try {
    const entries = fs.readdirSync(dirPath, { withFileTypes: true });
    
    entries.forEach((entry, index) => {
      const entryPath = path.join(dirPath, entry.name);
      const isLast = index === entries.length - 1;
      
      if (entry.isDirectory()) {
        structure += `${indent}  ${isLast ? '└──' : '├──'} ${entry.name}/\n`;
        structure += getDirectoryStructure(entryPath, depth + 1, maxDepth);
      } else {
        structure += `${indent}  ${isLast ? '└──' : '├──'} ${entry.name}\n`;
      }
    });
  } catch (err) {
    structure += `${indent}  └── [无法访问: ${err.message}]\n`;
  }
  
  return structure;
}

// 使用示例
const rootDir = process.argv[2] || '.'; // 默认当前目录
const maxDepth = parseInt(process.argv[3]) || 3; // 默认最大深度3

const structure = getDirectoryStructure(rootDir, 0, maxDepth);
console.log(structure);    