// pages/index/index.js
Page({
  data: {
    // 道具库图片
    propImages: [
      'https://picsum.photos/600/400?random=4',
      'https://picsum.photos/600/400?random=5',
      'https://picsum.photos/600/400?random=6'
    ],
    // 酒店联盟数据
    hotels: [
      { id: 1, name: '星光大酒店', image: 'https://picsum.photos/300/200?random=7' },
      { id: 2, name: '影视度假村', image: 'https://picsum.photos/300/200?random=8' },
      { id: 3, name: '基地公寓', image: 'https://picsum.photos/300/200?random=9' },
      { id: 4, name: '影人酒店', image: 'https://picsum.photos/300/200?random=10' },
      { id: 5, name: '星梦客栈', image: 'https://picsum.photos/300/200?random=11' },
      { id: 6, name: '光影酒店', image: 'https://picsum.photos/300/200?random=12' }
    ],
    // 荣誉数据
    honors: [
      { id: 1, name: '最佳影视基地', image: 'https://picsum.photos/300/200?random=13' },
      { id: 2, name: '最具影响力基地', image: 'https://picsum.photos/300/200?random=14' },
      { id: 3, name: '优秀合作单位', image: 'https://picsum.photos/300/200?random=15' },
      { id: 4, name: 'AAA级影视基地', image: 'https://picsum.photos/300/200?random=16' }
    ],
    // 代表作品
    works: [
      { id: 1, name: '《大明宫词》', image: 'https://picsum.photos/300/400?random=17' },
      { id: 2, name: '《琅琊榜》', image: 'https://picsum.photos/300/400?random=18' },
      { id: 3, name: '《甄嬛传》', image: 'https://picsum.photos/300/400?random=19' },
      { id: 4, name: '《长安十二时辰》', image: 'https://picsum.photos/300/400?random=20' },
      { id: 5, name: '《如懿传》', image: 'https://picsum.photos/300/400?random=21' },
      { id: 6, name: '《芈月传》', image: 'https://picsum.photos/300/400?random=22' }
    ]
  },
  onLoad: function() {
    // 页面加载时执行
    console.log('页面加载完成');
  },
  onReady: function() {
    // 页面初次渲染完成时执行
  },
  onShow: function() {
    // 页面显示时执行
  },
  onHide: function() {
    // 页面隐藏时执行
  },
  onUnload: function() {
    // 页面卸载时执行
  },
  // 导航点击事件处理函数
  navigateToHome: function() {
    console.log('导航到首页');
  },
  navigateToScenes: function() {
    console.log('导航到场景展示');
    wx.navigateTo({
      url: '/pages/scenes/scenes'
    });
  },
  navigateToCases: function() {
    console.log('导航到案例展示');
  },
  navigateToContact: function() {
    console.log('导航到联系我们');
  }
})