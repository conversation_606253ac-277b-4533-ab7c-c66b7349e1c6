/* pages/index/index.wxss */
.container {
  background-color: #000;
  color: #fff;
  min-height: 100vh;
}

/* 视频容器样式 */
.video-container {
  width: 100%;
  height: 500rpx;
  background-color: #111;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20rpx 0;
}

.promo-video {
  width: 100%;
  height: 100%;
}

/* 公司信息样式 */
.company-info {
  display: flex;
  padding: 40rpx;
  background-color: #111;
  margin: 20rpx 0;
}

.logo-container {
  width: 100rpx;
  height: 100rpx;
  margin-right: 40rpx;
  align-items: center; /* 垂直居中 */
}

.logo {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 2rpx solid #ff0000;
}

.company-details {
  flex: 1;
}

.company-name {
  color: #ffffff;
  font-size: 50rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.company-desc {
  color: #ddd;
  font-size: 20rpx;
  line-height: 1.5;
}

/* 通用部分标题样式 */
.section-title {
  color: #ff0000;
  font-size: 36rpx;
  font-weight: bold;
  padding: 20rpx 40rpx;
  background-color: #1a1a1a;
  border-left: 6rpx solid #ff0000;
  margin: 20rpx 0;
}

/* 道具库轮播样式 */
.prop-gallery {
  padding: 0 20rpx;
}

.prop-swiper {
  height: 400rpx;
}

.prop-image {
  width: 100%;
  height: 100%;
  border-radius: 10rpx;
}

/* 酒店联盟网格样式 */
.hotel-alliance {
  padding: 0 20rpx;
}

.hotel-grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.hotel-item {
  width: 330rpx;
  margin-bottom: 20rpx;
  background-color: #1a1a1a;
  border-radius: 10rpx;
  overflow: hidden;
}

.hotel-image {
  width: 100%;
  height: 200rpx;
}

.hotel-name {
  padding: 10rpx;
  text-align: center;
  font-size: 28rpx;
}

/* 荣誉展示样式 */
.honors-section {
  padding: 0 20rpx;
}

.honors-grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.honor-item {
  width: 220rpx;
  margin-bottom: 20rpx;
  background-color: #1a1a1a;
  border-radius: 10rpx;
  overflow: hidden;
}

.honor-image {
  width: 100%;
  height: 150rpx;
}

.honor-name {
  padding: 10rpx;
  text-align: center;
  font-size: 24rpx;
}

/* 代表作品样式 */
.works-section {
  padding: 0 20rpx;
}

.works-scroll {
  white-space: nowrap;
  padding-bottom: 20rpx;
}

.works-container {
  display: inline-flex;
}

.work-item {
  width: 240rpx;
  margin-right: 20rpx;
  background-color: #1a1a1a;
  border-radius: 10rpx;
  overflow: hidden;
}

.work-image {
  width: 100%;
  height: 320rpx;
}

.work-name {
  padding: 10rpx;
  text-align: center;
  font-size: 28rpx;
  white-space: normal;
}

/* 联系信息样式 */
.contact-section {
  padding: 0 20rpx;
  margin-bottom: 40rpx;
}

.contact-info {
  background-color: #1a1a1a;
  padding: 30rpx;
  border-radius: 10rpx;
}

.contact-item {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.contact-label {
  color: #ff0000;
  font-size: 30rpx;
  width: 120rpx;
}

.contact-value {
  color: #ddd;
  font-size: 30rpx;
}

.wechat-qrcode {
  width: 150rpx;
  height: 150rpx;
  margin-top: 10rpx;
}

.qrcode-image {
  width: 100%;
  height: 100%;
}

/* 页脚样式 */
.footer {
  background-color: #0a0a0a;
  text-align: center;
  padding: 20rpx;
  font-size: 24rpx;
  color: #666;
}