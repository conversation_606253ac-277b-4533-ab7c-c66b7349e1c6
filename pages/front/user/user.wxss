/* 基础样式 */
.container {
  background-color: #0a0a0a;
  min-height: 100vh;
  color: #ffffff;
  padding-bottom: 30rpx;
}

/* 用户信息头部 */
.user-header {
  background-color: #121212;
  padding: 40rpx 30rpx;
  display: flex;
  align-items: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.user-avatar-container {
  width: 140rpx;
  height: 140rpx;
  border-radius: 50%;
  overflow: hidden;
  border: 4rpx solid rgba(255, 255, 255, 0.1);
  margin-right: 30rpx;
}

.user-avatar {
  width: 100%;
  height: 100%;
}

.user-info {
  flex: 1;
}

.user-name {
  font-size: 36rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 10rpx;
}

.user-role {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.6);
}

.edit-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 功能菜单区 */
.function-menu {
  margin: 30rpx;
  background-color: #121212;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.5);
}

.system-menu {
  margin: 0 30rpx 30rpx;
  background-color: #121212;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.5);
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 28rpx 30rpx;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  transition: background-color 0.2s;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item:active {
  background-color: rgba(255, 255, 255, 0.05);
}

.menu-icon {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
}

.menu-text {
  flex: 1;
  font-size: 30rpx;
}

/* 退出登录按钮 */
.logout-btn {
  width: calc(100% - 60rpx);
  margin: 0 30rpx;
  height: 90rpx;
  line-height: 90rpx;
  background-color: transparent;
  border: 1px solid #ff3b30;
  color: #ff3b30;
  font-size: 30rpx;
  border-radius: 15rpx;
  transition: all 0.2s;
}

.logout-btn:active {
  background-color: rgba(255, 59, 48, 0.1);
}
    