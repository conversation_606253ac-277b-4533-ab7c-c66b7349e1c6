const app = getApp();

Page({
  data: {
    userInfo: {} // 用户信息
  },

  onLoad() {
    // 获取用户信息
    this.getUserInfo();
  },

  onShow() {
    // 页面显示时刷新用户信息
    this.getUserInfo();
  },

  // 获取用户信息
  getUserInfo() {
    // 从全局变量获取用户信息（实际项目中可能需要从缓存或接口获取）
    if (app.globalData.userInfo) {
      this.setData({
        userInfo: app.globalData.userInfo
      });
    } else {
      // 如果没有用户信息，尝试从缓存获取
      const userInfo = wx.getStorageSync('userInfo');
      if (userInfo) {
        this.setData({
          userInfo
        });
      }
    }
  },

  // 编辑个人资料
  async editProfile() {
    if (!app.globalData.hasLogin) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    try {
      const result = await this.showNicknameEditor();
      if (result) {
        // 调用更新用户信息云函数
        const updateResult = await wx.cloud.callFunction({
          name: 'api',
          data: {
            action: 'updateuser',
            nickname: result
          }
        });

        if (updateResult.result && updateResult.result.code === 0) {
          // 更新成功，刷新用户信息
          app.globalData.userInfo = updateResult.result.data;
          this.setData({
            userInfo: updateResult.result.data
          });

          wx.showToast({
            title: '昵称更新成功',
            icon: 'success'
          });
        } else {
          wx.showToast({
            title: '更新失败',
            icon: 'none'
          });
        }
      }
    } catch (error) {
      console.error('编辑个人资料失败:', error);
      wx.showToast({
        title: '操作失败',
        icon: 'none'
      });
    }
  },

  // 显示昵称编辑器
  showNicknameEditor() {
    return new Promise((resolve) => {
      wx.showModal({
        title: '编辑昵称',
        content: '请输入新的昵称',
        editable: true,
        placeholderText: this.data.userInfo.nickname || '请输入昵称',
        success: (res) => {
          if (res.confirm && res.content && res.content.trim()) {
            resolve(res.content.trim());
          } else {
            resolve(null);
          }
        },
        fail: () => {
          resolve(null);
        }
      });
    });
  },

  // 跳转到我的预约
  navToMyBookings() {
    wx.navigateTo({
      url: '/pages/front/my-bookings/my-bookings'
    });
  },

  // 跳转到收藏场景
  navToFavorites() {
    wx.navigateTo({
      url: '/pages/front/favorites/favorites'
    });
  },

  // 跳转到浏览历史
  navToHistory() {
    wx.navigateTo({
      url: '/pages/front/history/history'
    });
  },

  // 联系我们
  contactUs() {
    wx.makePhoneCall({
      phoneNumber: '13800138000'
    });
  },

  // 跳转到管理员登录
  navToAdminLogin() {
    wx.navigateTo({
      url: '/pages/manage/login/login'
    });
  },

  // 处理用户登录
  async handleLogin() {
    try {
      // 获取用户授权
      const userProfile = await this.getUserProfile();
      
      if (userProfile) {
        // 自动处理昵称：如果是"微信用户"就改为"影视用户"
        let finalNickname = userProfile.nickName;
        if (userProfile.nickName === '微信用户') {
          finalNickname = '影视用户';
        }

        // 调用登录云函数
        const result = await wx.cloud.callFunction({
          name: 'api',
          data: {
            action: 'login',
            nickname: finalNickname,
            avatarUrl: userProfile.avatarUrl
          }
        });

        console.log('登录结果:', result);

        if (result.result && result.result.code === 0) {
          // 登录成功，更新全局状态和页面状态
          app.globalData.userInfo = result.result.data;
          app.globalData.hasLogin = true;
          app.globalData.isAdmin = result.result.data.isAdmin || false;

          this.setData({
            userInfo: result.result.data
          });

          wx.showToast({
            title: '登录成功',
            icon: 'success'
          });
        } else {
          wx.showToast({
            title: '登录失败',
            icon: 'none'
          });
        }
      }
    } catch (error) {
      console.error('登录失败:', error);
      wx.showToast({
        title: '登录失败',
        icon: 'none'
      });
    }
  },

  // 获取用户信息
  getUserProfile() {
    return new Promise((resolve, reject) => {
      wx.getUserProfile({
        desc: '用于完善用户资料',
        success: (res) => {
          console.log('获取用户信息成功:', res.userInfo);
          resolve(res.userInfo);
        },
        fail: (error) => {
          console.error('获取用户信息失败:', error);
          if (error.errMsg.includes('deny')) {
            wx.showModal({
              title: '提示',
              content: '需要授权才能登录，请重新点击登录按钮',
              showCancel: false
            });
          }
          resolve(null);
        }
      });
    });
  },

  // 跳转到帮助中心
  navToHelp() {
    wx.navigateTo({
      url: '/pages/front/help/help'
    });
  },

  // 跳转到关于我们
  navToAbout() {
    wx.navigateTo({
      url: '/pages/front/about/about'
    });
  },

  // 退出登录
  logout() {
    wx.showModal({
      title: '确认退出',
      content: '确定要退出当前账号吗？',
      success: (res) => {
        if (res.confirm) {
          // 清除用户信息和登录状态
          app.globalData.userInfo = null;
          app.globalData.hasLogin = false;
          app.globalData.isAdmin = false;
          wx.removeStorageSync('userInfo');
          
          // 更新页面状态
          this.setData({
            userInfo: {}
          });

          wx.showToast({
            title: '已退出登录',
            icon: 'success'
          });

          // 如果当前在管理页面，跳转到登录页
          const pages = getCurrentPages();
          const currentPage = pages[pages.length - 1];
          if (currentPage.route.includes('manage')) {
            wx.reLaunch({
              url: '/pages/manage/login/login'
            });
          }
        }
      }
    });
  }
});
    