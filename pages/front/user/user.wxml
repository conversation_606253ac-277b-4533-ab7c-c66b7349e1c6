<view class="container">
  <!-- 已登录状态 -->
  <block wx:if="{{userInfo.nickname || userInfo.nickName}}">
    <!-- 用户信息头部 -->
    <view class="user-header">
      <view class="user-avatar-container">
        <image 
          class="user-avatar" 
          src="{{userInfo.avatarUrl || '/images/default-avatar.png'}}"
          mode="cover"
        ></image>
      </view>
      <view class="user-info">
        <text class="user-name">{{userInfo.nickname || userInfo.nickName || '影视用户'}}</text>
        <text class="user-role">剧组选景负责人</text>
      </view>
      <view class="edit-btn" bindtap="editProfile">
        <icon type="edit" size="28" color="#fff"></icon>
      </view>
    </view>
  </block>

  <!-- 未登录状态 -->
  <block wx:else>
    <view class="login-prompt">
      <view class="login-icon">👤</view>
      <text class="login-title">欢迎来到影视基地</text>
      <text class="login-desc">登录后享受更多专业服务</text>
      
      <view class="login-buttons">
        <button class="login-btn primary" bindtap="handleLogin">微信登录</button>
        <button class="login-btn secondary" bindtap="navToAdminLogin">管理员登录</button>
      </view>
    </view>
  </block>

  <!-- 功能菜单区 -->
  <view class="function-menu">
    <!-- 已登录用户显示个人功能 -->
    <block wx:if="{{userInfo.nickname || userInfo.nickName}}">
      <!-- 我的预约 -->
      <view class="menu-item" bindtap="navToMyBookings">
        <view class="menu-icon">
          <icon type="calendar" size="32" color="#ff3b30"></icon>
        </view>
        <text class="menu-text">我的预约</text>
        <icon type="arrowright" size="24" color="#999"></icon>
      </view>

      <!-- 收藏场景 -->
      <view class="menu-item" bindtap="navToFavorites">
        <view class="menu-icon">
          <icon type="star" size="32" color="#ff3b30"></icon>
        </view>
        <text class="menu-text">收藏场景</text>
        <icon type="arrowright" size="24" color="#999"></icon>
      </view>

      <!-- 浏览历史 -->
      <view class="menu-item" bindtap="navToHistory">
        <view class="menu-icon">
          <icon type="clock" size="32" color="#ff3b30"></icon>
        </view>
        <text class="menu-text">浏览历史</text>
        <icon type="arrowright" size="24" color="#999"></icon>
      </view>
    </block>

    <!-- 联系我们 - 所有用户都显示 -->
    <view class="menu-item" bindtap="contactUs">
      <view class="menu-icon">
        <icon type="phone" size="32" color="#ff3b30"></icon>
      </view>
      <text class="menu-text">联系我们</text>
      <icon type="arrowright" size="24" color="#999"></icon>
    </view>
  </view>

  <!-- 系统菜单区 -->
  <view class="system-menu">
    <!-- 帮助中心 -->
    <view class="menu-item" bindtap="navToHelp">
      <view class="menu-icon">
        <icon type="info" size="32" color="#999"></icon>
      </view>
      <text class="menu-text">帮助中心</text>
      <icon type="arrowright" size="24" color="#999"></icon>
    </view>

    <!-- 关于我们 -->
    <view class="menu-item" bindtap="navToAbout">
      <view class="menu-icon">
        <icon type="location" size="32" color="#999"></icon>
      </view>
      <text class="menu-text">关于我们</text>
      <icon type="arrowright" size="24" color="#999"></icon>
    </view>
  </view>

  <!-- 退出登录 - 仅已登录时显示 -->
  <button class="logout-btn" bindtap="logout" wx:if="{{userInfo.nickname || userInfo.nickName}}">退出登录</button>
</view>
    