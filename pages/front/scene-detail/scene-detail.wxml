<view class="detail-container">
  <!-- 图片轮播（主图+详情图） -->
  <swiper class="img-swiper" indicator-dots circular>
    <block wx:for="{{scene.images}}" wx:key="*this">
      <swiper-item>
        <image 
          class="swiper-img" 
          src="{{item}}"
          mode="widthFix"
          bindtap="previewImage"
          data-url="{{item}}"
        ></image>
      </swiper-item>
    </block>
  </swiper>

  <!-- 平面图区域 -->
  <view class="floor-plan-section" wx:if="{{scene.floorPlan}}">
    <text class="section-title">平面图</text>
    <image 
      class="floor-plan-img" 
      src="{{scene.floorPlan}}"
      mode="widthFix"
      bindtap="previewImage"
      data-url="{{scene.floorPlan}}"
    ></image>
  </view>

  <!-- 基本信息 -->
  <view class="basic-info">
    <text class="scene-title">{{scene.name}}</text>
    <text class="scene-type">{{scene.type}}</text>
    <text class="scene-desc">{{scene.description}}</text>
  </view>

  <!-- 价格表 -->
  <view class="price-table-section">
    <text class="section-title">价格详情</text>
    <view class="price-table">
      <view class="price-row">
        <text class="price-label">1小时</text>
        <text class="price-value">¥{{scene.prices.hourly}}</text>
      </view>
      <view class="price-row">
        <text class="price-label">半天（4小时）</text>
        <text class="price-value">¥{{scene.prices.halfDay}}</text>
      </view>
      <view class="price-row">
        <text class="price-label">全天（8小时）</text>
        <text class="price-value">¥{{scene.prices.fullDay}}</text>
      </view>
      <view class="price-row" wx:if="{{scene.prices.monthly}}">
        <text class="price-label">包月</text>
        <text class="price-value">¥{{scene.prices.monthly}}</text>
      </view>
    </view>
  </view>

  <!-- 底部操作栏（如预约按钮） -->
  <view class="bottom-bar">
    <button class="book-btn" style="background-color: {{mainColor}}">
      立即预约
    </button>
  </view>
</view>