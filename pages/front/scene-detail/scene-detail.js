Page({
  data: {
    scene: {},           // 场景详情数据
    sceneId: '',         // 当前场景ID
    mainColor: '#3275F4' // 主色调
  },

  onLoad(options) {
    const app = getApp();
    this.setData({
      sceneId: options.sceneId,
      mainColor: app.globalData.mainColor || '#3275F4'
    });
    this.loadSceneDetail();
  },

  // 加载场景详情
  async loadSceneDetail() {
    wx.showLoading({ title: '加载中...' });
    try {
      // 直接调用云函数获取场景详情
      const { result } = await wx.cloud.callFunction({
        name: 'api',
        data: {
          action: 'getscenedetail',
          sceneId: this.data.sceneId
        }
      });

      if (result.success) {
        this.setData({ scene: result.scene });
      } else {
        console.error('加载场景详情失败:', result.error);
        wx.showToast({ title: '加载失败', icon: 'none' });
      }
    } catch (err) {
      console.error('加载详情失败:', err);
      wx.showToast({ title: '加载失败', icon: 'none' });
    } finally {
      wx.hideLoading();
    }
  },

  // 预览图片
  previewImage(e) {
    const currentUrl = e.currentTarget.dataset.url;
    wx.previewImage({
      current: currentUrl,
      urls: [...this.data.scene.images, this.data.scene.floorPlan].filter(Boolean)
    });
  }
});