/* 继承主色调 */
@import "/pages/index/index.wxss";

.detail-container {
  background-color: white;
  min-height: 100vh;
}

/* 图片轮播 */
.img-swiper {
  width: 100%;
}

.swiper-img {
  width: 100%;
  height: auto;
}

/* 平面图区域 */
.floor-plan-section {
  padding: 24rpx;
  border-top: 1px solid #f1f1f1;
}

.floor-plan-img {
  width: 100%;
  height: auto;
  margin-top: 16rpx;
  border-radius: 8rpx;
}

/* 基本信息 */
.basic-info {
  padding: 24rpx;
  border-top: 1px solid #f1f1f1;
}

.scene-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 12rpx;
}

.scene-type {
  display: inline-block;
  padding: 4rpx 16rpx;
  background-color: rgba(50, 117, 244, 0.1); /* 主色调透明背景 */
  color: var(--main-color, #3275F4);
  font-size: 24rpx;
  border-radius: 20rpx;
  margin-bottom: 16rpx;
}

.scene-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

/* 价格表区域 */
.price-table-section {
  padding: 24rpx;
  border-top: 1px solid #f1f1f1;
  background-color: #fafafa;
}

/* 区域标题通用样式 */
.section-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  display: block;
  padding-bottom: 16rpx;
  border-bottom: 2px solid var(--main-color, #3275F4); /* 主色调下划线 */
}

/* 价格表 */
.price-table {
  margin-top: 20rpx;
}

.price-row {
  display: flex;
  justify-content: space-between;
  padding: 18rpx 0;
  border-bottom: 1px dashed #eee;
}

.price-label {
  font-size: 28rpx;
  color: #666;
}

.price-value {
  font-size: 28rpx;
  font-weight: bold;
  color: #ff4d4f;
}

/* 底部操作栏 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 16rpx 24rpx;
  background-color: white;
  box-shadow: 0 -2rpx 10rpx rgba(0,0,0,0.05);
  z-index: 10;
}

.book-btn {
  width: 100%;
  height: 90rpx;
  border-radius: 4
}