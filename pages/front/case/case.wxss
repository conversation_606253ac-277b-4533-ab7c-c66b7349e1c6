/* 基础样式 */
.container {
  padding: 20rpx;
  background-color: #0a0a0a;
  min-height: 100vh;
  color: #ffffff;
}

/* 标题栏 */
.title-bar {
  padding: 24rpx 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  margin-bottom: 30rpx;
}
.title {
  font-size: 38rpx;
  font-weight: 600;
  letter-spacing: 1rpx;
}

/* 案例列表 */
.case-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.case-item {
  display: flex;
  background-color: #121212;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.5);
  transition: all 0.3s ease;
}
.case-item:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.6);
}

.case-img {
  width: 240rpx;
  height: 180rpx;
  flex-shrink: 0;
}

.case-info {
  flex: 1;
  padding: 20rpx;
  display: flex;
  flex-direction: column;
}

.case-title {
  font-size: 30rpx;
  font-weight: 600;
  margin-bottom: 10rpx;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.case-desc {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
  flex: 1;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  line-height: 1.5;
}

.case-meta {
  display: flex;
  justify-content: space-between;
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.5);
  margin-top: 10rpx;
}

/* 空状态/加载状态/加载更多 */
.empty-tip {
  text-align: center;
  padding: 200rpx 0;
  color: rgba(255, 255, 255, 0.5);
  font-size: 28rpx;
}

.loading-tip {
  text-align: center;
  padding: 100rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
}
.loading-tip text {
  color: rgba(255, 255, 255, 0.6);
  font-size: 26rpx;
}

.load-more {
  text-align: center;
  padding: 30rpx 0;
  color: #ff0000; /* 红色强调 */
  font-size: 28rpx;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  margin-top: 20rpx;
  letter-spacing: 1rpx;
}
.load-more:active {
  color: #ff4d4d;
}