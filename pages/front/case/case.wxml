<view class="container">
  <!-- 标题栏 -->
  <view class="title-bar">
    <text class="title">成功案例</text>
  </view>

  <!-- 案例列表 -->
  <view class="case-list">
    <block wx:for="{{cases}}" wx:key="id">
      <view class="case-item" bindtap="navToCaseDetail" data-id="{{item.id}}">
        <image 
          class="case-img" 
          src="{{item.image}}" 
          mode="cover"
        ></image>
        <view class="case-info">
          <text class="case-title">{{item.title}}</text>
          <text class="case-desc">{{item.description}}</text>
          <view class="case-meta">
            <text class="case-date">{{item.date}}</text>
            <text class="case-scene">使用场景: {{item.sceneCount}}个</text>
          </view>
        </view>
      </view>
    </block>
  </view>

  <!-- 空状态/加载状态 -->
  <view wx:if="{{cases.length === 0 && !loading}}" class="empty-tip">
    暂无案例数据
  </view>
  <view wx:if="{{loading}}" class="loading-tip">
    <van-loading size="30" color="#fff"></van-loading>
    <text>加载中...</text>
  </view>
  <view wx:if="{{hasMore && !loading}}" class="load-more" bindtap="loadMore">
    加载更多
  </view>
</view>