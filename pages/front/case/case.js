const app = getApp();

Page({
  data: {
    cases: [],        // 案例列表
    loading: false,   // 加载状态
    page: 1,          // 当前页
    pageSize: 10,     // 每页数量
    hasMore: true     // 是否有更多
  },

  onLoad() {
    this.loadCases();
  },

  // 加载案例数据
  async loadCases() {
    if (this.data.loading) return;
    this.setData({ loading: true });

    try {
      // 实际项目中替换为真实接口
      const mockData = {
        code: 0,
        data: {
          list: [
            {
              id: 1,
              title: '《山城往事》电视剧拍摄',
              description: '使用了我基地的年代住家、街道场景等多个场地完成拍摄，获得广泛好评',
              image: 'https://picsum.photos/600/400?random=29',
              date: '2024-03-15',
              sceneCount: 5
            },
            {
              id: 2,
              title: '《迷雾追踪》电影取景',
              description: '主要使用了警局、医院等场景，完美呈现了剧情需要的紧张氛围',
              image: 'https://picsum.photos/600/400?random=30',
              date: '2024-01-20',
              sceneCount: 3
            }
          ],
          totalPages: 3
        }
      };

      const res = mockData;
      if (res.code === 0) {
        const newList = this.data.page === 1 
          ? res.data.list 
          : [...this.data.cases, ...res.data.list];
        
        this.setData({
          cases: newList,
          hasMore: this.data.page < res.data.totalPages,
          page: this.data.page + 1
        });
      } else {
        wx.showToast({ title: res.message || '加载失败', icon: 'none' });
      }
    } catch (err) {
      console.error('加载案例失败:', err);
      wx.showToast({ title: '加载失败', icon: 'none' });
    } finally {
      this.setData({ loading: false });
    }
  },

  // 加载更多
  loadMore() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadCases();
    }
  },

  // 跳转到案例详情
  navToCaseDetail(e) {
    const caseId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/front/detail/detail?caseId=${caseId}`
    });
  }
});