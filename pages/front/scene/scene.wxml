<view class="container">
  <!-- 页面标题 -->
  <view class="page-header">
    <text class="header-title">影视场景橱窗</text>
    <text class="header-subtitle">精选场景 · 专业拍摄</text>
  </view>

  <!-- 场景橱窗展示区 -->
  <view class="scene-showcase">
    <block wx:for="{{sceneList}}" wx:key="_id">
      <view class="showcase-item" bindtap="navToDetail" data-id="{{item._id}}">
        <!-- 图片容器（橱窗核心） -->
        <view class="item-image-container">
          <image 
            class="item-image" 
            src="{{item.images[0] || '/images/default-scene.png'}}"
            mode="cover"
          ></image>
          
          <!-- 价格标签 -->
          <view class="price-badge">
            <text>¥{{item.prices.hourly}}</text>
            <text class="price-unit">/小时</text>
          </view>
          
          <!-- 快速操作按钮（悬停显示） -->
          <view class="quick-actions">
            <!-- 修复事件绑定错误：将 bindtap.stop 改为 bindtap -->
            <view class="action-btn favorite-btn" bindtap="toggleFavorite" data-id="{{item._id}}">
              <icon type="{{item.isFavorite ? 'filled' : 'empty'}}" size="28" color="#fff"></icon>
              <text>{{item.isFavorite ? '已收藏' : '收藏'}}</text>
            </view>
            <view class="action-btn preview-btn" bindtap="navToDetail" data-id="{{item._id}}">
              <icon type="eye" size="28" color="#fff"></icon>
              <text>预览</text>
            </view>
          </view>
        </view>
        
        <!-- 场景信息 -->
        <view class="item-info">
          <text class="scene-name">{{item.name}}</text>
          <text class="scene-type">{{item.type}}</text>
          <text class="scene-desc">{{item.description}}</text>
        </view>
      </view>
    </block>
  </view>

  <!-- 加载更多 -->
  <view wx:if="{{hasMore && !loading}}" class="load-more" bindtap="loadMore">
    <view class="loader"></view>
    <text>加载更多场景</text>
  </view>

  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-overlay">
    <view class="loading-spinner"></view>
  </view>
</view>
