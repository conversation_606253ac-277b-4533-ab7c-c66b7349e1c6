/* 基础橱窗风格样式 */
.window-container {
  background-color: #0a0a0a;
  min-height: 100vh;
  color: #ffffff;
  padding-bottom: 60rpx;
}

/* 页面标题 */
.page-header {
  padding: 60rpx 30rpx 40rpx;
  text-align: center;
}
.header-title {
  font-size: 44rpx;
  font-weight: bold;
  letter-spacing: 2rpx;
  position: relative;
  padding-bottom: 15rpx;
}
.header-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80rpx;
  height: 4rpx;
  background-color: #ff3b30;
  border-radius: 2rpx;
}
.header-subtitle {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.6);
  margin-top: 15rpx;
  display: block;
}

/* 橱窗展示区 */
.scene-showcase {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 30rpx;
  padding: 0 20rpx;
}

/* 橱窗项目 */
.showcase-item {
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
}
.showcase-item:active {
  transform: translateY(4rpx);
}

/* 图片容器（橱窗核心） */
.item-image-container {
  position: relative;
  width: 100%;
  height: 340rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 10rpx 25rpx rgba(0, 0, 0, 0.5);
  margin-bottom: 18rpx;
}
.item-image {
  width: 100%;
  height: 100%;
  transition: transform 0.7s ease;
}
.showcase-item:hover .item-image {
  transform: scale(1.08);
}

/* 悬浮信息卡 */
.floating-info {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 20rpx;
  background: linear-gradient(to top, rgba(0,0,0,0.9), transparent);
  z-index: 2;
}
.scene-name {
  font-size: 30rpx;
  font-weight: 600;
  display: block;
  text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.5);
}
.scene-type {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.8);
  background-color: rgba(255, 59, 48, 0.8);
  padding: 2rpx 12rpx;
  border-radius: 12rpx;
  margin-top: 8rpx;
  display: inline-block;
}

/* 价格标签 */
.price-badge {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  background-color: #ff3b30;
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 26rpx;
  font-weight: bold;
  display: flex;
  align-items: baseline;
  gap: 5rpx;
  z-index: 2;
  box-shadow: 0 4rpx 8rpx rgba(0,0,0,0.3);
}
.price-unit {
  font-size: 20rpx;
  font-weight: normal;
}

/* 快速操作按钮 */
.quick-actions {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  gap: 15rpx;
  z-index: 3;
  opacity: 0;
  transition: opacity 0.3s ease;
}
.showcase-item:hover .quick-actions {
  opacity: 1;
}
.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 90rpx;
  height: 90rpx;
  background-color: rgba(0,0,0,0.7);
  backdrop-filter: blur(8rpx);
  border-radius: 50%;
  font-size: 20rpx;
  color: white;
  transition: all 0.2s ease;
}
.action-btn:active {
  transform: scale(0.9);
  background-color: rgba(255, 59, 48, 0.8);
}
.favorite-btn {
  border: 1px solid rgba(255,255,255,0.3);
}
.preview-btn {
  border: 1px solid rgba(255,255,255,0.3);
}

/* 场景简介 */
.item-description {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
  padding: 0 5rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  line-height: 1.5;
}

/* 加载更多 */
.load-more {
  margin: 60rpx auto 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20rpx;
  color: rgba(255, 255, 255, 0.6);
  font-size: 26rpx;
}
.loader {
  width: 40rpx;
  height: 40rpx;
  border: 3rpx solid rgba(255, 255, 255, 0.2);
  border-top-color: #ff3b30;
  border-radius: 50%;
  animation: spin 1.5s linear infinite;
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0,0,0,0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
}
.loading-spinner {
  width: 80rpx;
  height: 80rpx;
  border: 5rpx solid rgba(255, 255, 255, 0.1);
  border-top-color: #ff3b30;
  border-radius: 50%;
  animation: spin 1.2s linear infinite;
}

/* 旋转动画 */
@keyframes spin {
  to { transform: rotate(360deg); }
}
    