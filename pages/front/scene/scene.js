// pages/front/scene/scene.js
Page({
  // 移除模块导入，直接在页面中实现云函数调用
  data: {
    sceneList: [],         // 全部场景数据
    loading: false,        // 加载状态
    page: 1,               // 当前页
    pageSize: 6,           // 每页6个
    hasMore: true          // 是否有更多数据
  },

  onLoad() {
    // 初始化加载场景
    this.loadScenes();
  },

  // 加载场景数据
  async loadScenes() {
    if (this.data.loading) return;
    this.setData({ loading: true });

    try {
      // 直接调用云函数
      console.log('开始获取场景数据，页码:', this.data.page);
      const { result } = await wx.cloud.callFunction({
        name: 'api',
        data: {
          action: 'getscenes',
          page: this.data.page,
          pageSize: this.data.pageSize
        }
      });
      console.log('云函数调用结果:', result);

      if (result.success) {
        const newScenes = this.data.page === 1 ? result.scenes : [...this.data.sceneList, ...result.scenes];

        this.setData({
          sceneList: newScenes,
          hasMore: newScenes.length < result.total,
          page: this.data.page + 1,
          loading: false
        });
      } else {
        console.error('加载场景失败:', result.error);
        wx.showToast({ title: '加载失败', icon: 'none' });
        this.setData({ loading: false });
      }
    } catch (err) {
      console.error('加载场景失败:', err);
      wx.showToast({ title: '加载失败: ' + err.message, icon: 'none' });
      this.setData({ loading: false });
    }
  },

  // 切换收藏状态
  toggleFavorite(e) {
    const sceneId = e.currentTarget.dataset.id;
    const newSceneList = this.data.sceneList.map(scene => {
      if (scene._id === sceneId) {
        return { ...scene, isFavorite: !scene.isFavorite };
      }
      return scene;
    });

    this.setData({ sceneList: newSceneList });
    const scene = newSceneList.find(s => s._id === sceneId);
    wx.showToast({
      title: scene.isFavorite ? '已收藏' : '已取消收藏',
      icon: 'none'
    });
  },

  // 加载更多
  loadMore() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadScenes();
    }
  },

  // 跳转到详情页
  navToDetail(e) {
    const sceneId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/front/scene-detail/scene-detail?sceneId=${sceneId}`
    });
  }
});
