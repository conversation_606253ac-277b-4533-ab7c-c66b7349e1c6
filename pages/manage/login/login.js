// pages/manage/login/login.js
Page({
  data: {
    loading: false,
    userInfo: null,
    hasUserInfo: false
  },

  onLoad(options) {
    // 检查是否已登录
    this.checkLoginStatus();
  },

  onShow() {
    // 页面显示时再次检查登录状态
    this.checkLoginStatus();
  },

  // 检查登录状态
  checkLoginStatus() {
    const app = getApp();
    if (app.globalData.hasLogin && app.globalData.userInfo) {
      // 已登录，更新页面状态
      this.setData({
        userInfo: app.globalData.userInfo,
        hasUserInfo: true
      });
      
      // 延迟跳转，让用户看到登录状态
      setTimeout(() => {
        wx.redirectTo({
          url: '/pages/manage/dashboard/dashboard'
        });
      }, 1000);
    } else {
      // 未登录，确保页面状态正确
      this.setData({
        userInfo: null,
        hasUserInfo: false
      });
    }
  },

  // 微信授权登录
  async handleWxLogin() {
    if (this.data.loading) return;
    try {
      this.setData({ loading: true });

      // 获取用户授权
      const userProfile = await this.getUserProfile();
      if (userProfile) {
        // 自动处理昵称：如果是"微信用户"就改为"影视用户"
        let finalNickname = userProfile.nickName;
        if (userProfile.nickName === '微信用户') {
          finalNickname = '影视用户';
        }

        // 调用登录云函数
        const result = await wx.cloud.callFunction({
          name: 'api',
          data: {
            action: 'login',
            nickname: finalNickname,
            avatarUrl: userProfile.avatarUrl
          }
        });

        console.log('登录结果:', result);
        if (result.result && result.result.code === 0) {
          // 登录成功，更新全局状态
          const app = getApp();
          app.globalData.userInfo = result.result.data;
          app.globalData.hasLogin = true;
          app.globalData.isAdmin = result.result.data.isAdmin || false;

          // 更新页面状态
          this.setData({
            userInfo: {
              ...userProfile,
              nickName: finalNickname
            },
            hasUserInfo: true
          });

          wx.showToast({
            title: '登录成功',
            icon: 'success'
          });

          // 跳转到管理页面
          setTimeout(() => {
            wx.redirectTo({
              url: '/pages/manage/dashboard/dashboard'
            });
          }, 1500);
        } else {
          wx.showToast({
            title: '登录失败',
            icon: 'none'
          });
        }
      }
    } catch (error) {
      console.error('登录失败:', error);
      wx.showToast({
        title: '登录失败',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  // 获取用户信息
  getUserProfile() {
    return new Promise((resolve, reject) => {
      wx.getUserProfile({
        desc: '用于完善用户资料',
        success: (res) => {
          console.log('获取用户信息成功:', res.userInfo);
          this.setData({
            userInfo: res.userInfo,
            hasUserInfo: true
          });
          resolve(res.userInfo);
        },
        fail: (error) => {
          console.error('获取用户信息失败:', error);
          if (error.errMsg.includes('deny')) {
            wx.showModal({
              title: '提示',
              content: '需要授权才能登录，请重新点击登录按钮',
              showCancel: false
            });
          }
          resolve(null);
        }
      });
    });
  },

  // 退出登录
  handleLogout() {
    wx.showModal({
      title: '确认退出',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          const app = getApp();
          app.globalData.userInfo = null;
          app.globalData.hasLogin = false;
          app.globalData.isAdmin = false;

          wx.showToast({
            title: '已退出登录',
            icon: 'success'
          });

          // 刷新页面状态
          this.setData({
            userInfo: null,
            hasUserInfo: false
          });
        }
      }
    });
  }
})