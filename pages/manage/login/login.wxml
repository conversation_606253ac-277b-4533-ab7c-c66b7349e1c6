<view class="login-container">
  <view class="logo">
    <text class="logo-text">🎬</text>
  </view>
  <view class="title">影视基地管理系统</view>
  
  <!-- 未登录状态 -->
  <block wx:if="{{!hasUserInfo}}">
    <view class="login-desc">
      <text>使用微信账号登录管理系统</text>
    </view>
    
    <view class="login-btn" bindtap="handleWxLogin" disabled="{{loading}}">
      <text class="wechat-icon" wx:if="{{!loading}}">💬</text>
      <text>{{loading ? '登录中...' : '微信登录'}}</text>
      <loading hidden="{{!loading}}" class="loading-icon" size="24"></loading>
    </view>
  </block>
  
  <!-- 已登录状态 -->
  <block wx:else>
    <view class="user-info">
      <image src="{{userInfo.avatarUrl}}" class="avatar"></image>
      <text class="nickname">{{userInfo.nickName}}</text>
      <text class="login-success">✅ 登录成功</text>
    </view>
    
    <view class="logout-btn" bindtap="handleLogout">
      <text>退出登录</text>
    </view>
  </block>
  
  <view class="footer-text">
    <text>请使用微信授权登录</text>
  </view>
</view>