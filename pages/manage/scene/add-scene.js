// pages/manage/scene/add-scene.js
Page({
  data: {
    sceneName: '',
    sceneDesc: '',
    hourlyPrice: '',
    imageUrl: '',
    loading: false
  },

  onLoad(options) {
    // 检查是否已登录
    const adminInfo = wx.getStorageSync('adminInfo')
    if (!adminInfo || !adminInfo.isAdmin) {
      wx.showToast({ title: '请先登录', icon: 'none' })
      wx.redirectTo({
        url: '/pages/manage/login/login'
      })
      return
    }
  },

  // 选择图片
  chooseImage() {
    wx.chooseImage({
      count: 1,
      success: (res) => {
        const tempFilePath = res.tempFilePaths[0]
        this.setData({
          imageUrl: tempFilePath
        })
      }
    })
  },

  // 提交表单
  submitForm() {
    const { sceneName, sceneDesc, hourlyPrice, imageUrl } = this.data

    // 表单验证
    if (!sceneName.trim()) {
      wx.showToast({ title: '请输入场景名称', icon: 'none' })
      return
    }

    if (!hourlyPrice || isNaN(parseFloat(hourlyPrice))) {
      wx.showToast({ title: '请输入有效的小时价格', icon: 'none' })
      return
    }

    this.setData({ loading: true })

    // 如果有图片，先上传图片
    if (imageUrl) {
      this.uploadImage().then(imageUrl => {
        this.addScene(imageUrl)
      }).catch(err => {
        console.error('图片上传失败', err)
        wx.showToast({ title: '图片上传失败', icon: 'none' })
        this.setData({ loading: false })
      })
    } else {
      this.addScene('')
    }
  },

  // 上传图片
  uploadImage() {
    return new Promise((resolve, reject) => {
      const timestamp = Date.now()
      const cloudPath = `scenes/${timestamp}.png`

      wx.cloud.uploadFile({
        cloudPath: cloudPath,
        filePath: this.data.imageUrl,
        success: res => {
          resolve(res.fileID)
        },
        fail: err => {
          reject(err)
        }
      })
    })
  },

  // 添加场景
  addScene(imageUrl) {
    const { sceneName, sceneDesc, hourlyPrice } = this.data

    wx.cloud.callFunction({
      name: 'api',
      data: {
        action: 'addscene',
        name: sceneName,
        description: sceneDesc,
        prices: {
          hourly: parseFloat(hourlyPrice)
        },
        images: imageUrl ? [imageUrl] : []
      },
      success: res => {
        if (res.result && res.result.success) {
          wx.showToast({ title: '添加成功' })
          setTimeout(() => {
            wx.navigateBack()
          }, 1500)
        } else {
          wx.showToast({ title: '添加失败', icon: 'none' })
        }
      },
      fail: err => {
        console.error('添加场景失败', err)
        wx.showToast({ title: '添加失败', icon: 'none' })
      },
      complete: () => {
        this.setData({ loading: false })
      }
    })
  }
})