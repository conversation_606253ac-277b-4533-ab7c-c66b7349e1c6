// pages/manage/scene/scene.js
Page({
  data: {
    sceneList: [],
    loading: false
  },

  onLoad(options) {
    // 检查是否已登录
    const adminInfo = wx.getStorageSync('adminInfo')
    if (!adminInfo || !adminInfo.isAdmin) {
      wx.showToast({ title: '请先登录', icon: 'none' })
      wx.redirectTo({
        url: '/pages/manage/login/login'
      })
      return
    }
  },

  onShow() {
    // 每次显示页面时重新加载场景列表
    this.loadSceneList()
  },

  // 加载场景列表
  loadSceneList() {
    console.log('开始加载场景列表')
    this.setData({ loading: true })

    wx.cloud.callFunction({
      name: 'api',
      data: {
        action: 'getscenes'
      },
      success: (res) => {
        console.log('获取场景列表成功', res)
        if (res.result && res.result.success) {
          this.setData({
            sceneList: res.result.scenes || []
          })
          console.log('场景列表数据:', res.result.scenes)
        } else {
          console.error('获取场景列表失败，结果:', res.result)
          wx.showToast({ title: '获取场景列表失败', icon: 'none' })
        }
      },
      fail: (err) => {
        console.error('获取场景列表失败', err)
        wx.showToast({ title: '获取场景列表失败: ' + err.message, icon: 'none' })
      },
      complete: () => {
        console.log('获取场景列表完成')
        this.setData({ loading: false })
      }
    })
  },

  // 跳转到添加场景页面
  navToAddScene() {
    wx.navigateTo({
      url: '/pages/manage/scene/add-scene'
    })
  },

  // 跳转到编辑场景页面
  navToEditScene(e) {
    const sceneId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/manage/scene/edit-scene?id=${sceneId}`
    })
  },

  // 删除场景
  deleteScene(e) {
    const sceneId = e.currentTarget.dataset.id

    wx.showModal({
      title: '确认删除',
      content: '确定要删除这个场景吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({ loading: true })

          wx.cloud.callFunction({
            name: 'api',
            data: {
              action: 'deletescene',
              sceneId: sceneId
            },
            success: (res) => {
              if (res.result && res.result.success) {
                wx.showToast({ title: '删除成功' })
                // 重新加载场景列表
                this.loadSceneList()
              } else {
                wx.showToast({ title: '删除失败', icon: 'none' })
              }
            },
            fail: (err) => {
              console.error('删除场景失败', err)
              wx.showToast({ title: '删除失败', icon: 'none' })
            },
            complete: () => {
              this.setData({ loading: false })
            }
          })
        }
      }
    })
  }
})