// pages/manage/scene/edit-scene.js
Page({
  data: {
    sceneId: '',
    sceneName: '',
    sceneDesc: '',
    hourlyPrice: '',
    imageUrl: '',
    loading: false
  },

  onLoad(options) {
    // 检查是否已登录
    const adminInfo = wx.getStorageSync('adminInfo')
    if (!adminInfo || !adminInfo.isAdmin) {
      wx.showToast({ title: '请先登录', icon: 'none' })
      wx.redirectTo({
        url: '/pages/manage/login/login'
      })
      return
    }

    // 获取场景ID
    if (options.id) {
      this.setData({
        sceneId: options.id
      })
      this.loadSceneDetail()
    } else {
      wx.showToast({ title: '场景ID不存在', icon: 'none' })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
    }
  },

  // 加载场景详情
  loadSceneDetail() {
    this.setData({ loading: true })

    wx.cloud.callFunction({
      name: 'api',
      data: {
        action: 'getscenedetail',
        sceneId: this.data.sceneId
      },
      success: res => {
        if (res.result && res.result.scene) {
          const scene = res.result.scene
          this.setData({
            sceneName: scene.name,
            sceneDesc: scene.description || '',
            hourlyPrice: scene.prices?.hourly || '',
            imageUrl: scene.images && scene.images.length > 0 ? scene.images[0] : ''
          })
        } else {
          wx.showToast({ title: '获取场景详情失败', icon: 'none' })
        }
      },
      fail: err => {
        console.error('获取场景详情失败', err)
        wx.showToast({ title: '获取场景详情失败', icon: 'none' })
      },
      complete: () => {
        this.setData({ loading: false })
      }
    })
  },

  // 选择图片
  chooseImage() {
    wx.chooseImage({
      count: 1,
      success: (res) => {
        const tempFilePath = res.tempFilePaths[0]
        this.setData({
          imageUrl: tempFilePath
        })
      }
    })
  },

  // 提交表单
  submitForm() {
    const { sceneId, sceneName, sceneDesc, hourlyPrice, imageUrl } = this.data

    // 表单验证
    if (!sceneName.trim()) {
      wx.showToast({ title: '请输入场景名称', icon: 'none' })
      return
    }

    if (!hourlyPrice || isNaN(parseFloat(hourlyPrice))) {
      wx.showToast({ title: '请输入有效的小时价格', icon: 'none' })
      return
    }

    this.setData({ loading: true })

    // 如果有新图片，先上传图片
    if (imageUrl && !imageUrl.startsWith('cloud://')) {
      this.uploadImage().then(newImageUrl => {
        this.updateScene(newImageUrl)
      }).catch(err => {
        console.error('图片上传失败', err)
        wx.showToast({ title: '图片上传失败', icon: 'none' })
        this.setData({ loading: false })
      })
    } else {
      this.updateScene(imageUrl)
    }
  },

  // 上传图片
  uploadImage() {
    return new Promise((resolve, reject) => {
      const timestamp = Date.now()
      const cloudPath = `scenes/${timestamp}.png`

      wx.cloud.uploadFile({
        cloudPath: cloudPath,
        filePath: this.data.imageUrl,
        success: res => {
          resolve(res.fileID)
        },
        fail: err => {
          reject(err)
        }
      })
    })
  },

  // 更新场景
  updateScene(imageUrl) {
    const { sceneId, sceneName, sceneDesc, hourlyPrice } = this.data

    wx.cloud.callFunction({
      name: 'api',
      data: {
        action: 'updatescene',
        sceneId: sceneId,
        name: sceneName,
        description: sceneDesc,
        prices: {
          hourly: parseFloat(hourlyPrice)
        },
        images: imageUrl ? [imageUrl] : []
      },
      success: res => {
        if (res.result && res.result.success) {
          wx.showToast({ title: '更新成功' })
          setTimeout(() => {
            wx.navigateBack()
          }, 1500)
        } else {
          wx.showToast({ title: '更新失败', icon: 'none' })
        }
      },
      fail: err => {
        console.error('更新场景失败', err)
        wx.showToast({ title: '更新失败', icon: 'none' })
      },
      complete: () => {
        this.setData({ loading: false })
      }
    })
  }
})