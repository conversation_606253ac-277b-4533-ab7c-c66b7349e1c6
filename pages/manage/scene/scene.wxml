<view class="scene-manage-container">
  <view class="header">
    <text class="title">场景管理</text>
    <button class="add-btn" bindtap="navToAddScene">添加场景</button>
  </view>

  <view class="scene-list">
    <block wx:if="{{sceneList.length > 0}}">
      <view class="scene-item" wx:for="{{sceneList}}" wx:key="_id">
        <view class="scene-info">
          <image class="scene-img" src="{{item.images[0] || '/images/default-scene.png'}}" mode="aspectFit"></image>
          <view class="scene-detail">
            <text class="scene-name">{{item.name}}</text>
            <text class="scene-desc">{{item.description || '无描述'}}</text>
            <view class="scene-price">
              <text>价格: ¥{{item.prices.hourly}}/小时</text>
            </view>
          </view>
        </view>
        <view class="scene-actions">
          <button class="edit-btn" size="mini" bindtap="navToEditScene" data-id="{{item._id}}">编辑</button>
          <button class="delete-btn" size="mini" type="warn" bindtap="deleteScene" data-id="{{item._id}}">删除</button>
        </view>
      </view>
    </block>
    <view wx:else class="empty-tip">
      暂无场景数据
    </view>
  </view>
</view>