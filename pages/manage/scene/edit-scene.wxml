<view class="edit-scene-container">
  <view class="header">
    <text class="title">编辑场景</text>
  </view>

  <view class="form-container">
    <view class="form-group">
      <text class="label">场景名称</text>
      <input class="input" placeholder="请输入场景名称" model:value="{{sceneName}}" />
    </view>

    <view class="form-group">
      <text class="label">场景描述</text>
      <textarea class="textarea" placeholder="请输入场景描述" model:value="{{sceneDesc}}" />
    </view>

    <view class="form-group">
      <text class="label">小时价格</text>
      <input class="input" type="digit" placeholder="请输入小时价格" model:value="{{hourlyPrice}}" />
    </view>

    <view class="form-group">
      <text class="label">场景图片</text>
      <view class="upload-area" bindtap="chooseImage">
        <image class="upload-img" src="{{imageUrl || '/images/icons/upload.png'}}" mode="aspectFit"></image>
        <text class="upload-text">{{imageUrl ? '点击更换图片' : '点击上传图片'}}</text>
      </view>
    </view>

    <button class="submit-btn" bindtap="submitForm" disabled="{{loading}}">{{loading ? '提交中...' : '保存修改'}}</button>
  </view>
</view>