.edit-scene-container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  padding: 20rpx 0;
  border-bottom: 1rpx solid #eee;
  margin-bottom: 30rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.form-container {
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
}

.form-group {
  margin-bottom: 30rpx;
}

.label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.input {
  width: 100%;
  height: 80rpx;
  border: 1rpx solid #eee;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  background-color: #f9f9f9;
}

.textarea {
  width: 100%;
  height: 160rpx;
  border: 1rpx solid #eee;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  background-color: #f9f9f9;
}

.upload-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx 0;
  border: 1rpx dashed #ddd;
  border-radius: 8rpx;
  background-color: #f9f9f9;
}

.upload-img {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
}

.upload-text {
  font-size: 26rpx;
  color: #999;
}

.submit-btn {
  width: 100%;
  height: 90rpx;
  background-color: #3275F4;
  color: white;
  font-size: 32rpx;
  border-radius: 45rpx;
  margin-top: 20rpx;
}