.scene-manage-container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #eee;
  margin-bottom: 30rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.add-btn {
  background-color: #3275F4;
  color: white;
  font-size: 28rpx;
  padding: 0 30rpx;
  line-height: 70rpx;
  border-radius: 35rpx;
}

.scene-list {
  width: 100%;
}

.scene-item {
  display: flex;
  justify-content: space-between;
  background-color: white;
  border-radius: 16rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
}

.scene-info {
  display: flex;
  flex: 1;
}

.scene-img {
  width: 160rpx;
  height: 160rpx;
  border-radius: 10rpx;
  margin-right: 20rpx;
}

.scene-detail {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.scene-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.scene-desc {
  font-size: 26rpx;
  color: #666;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.scene-price {
  font-size: 26rpx;
  color: #ff4d4f;
  margin-top: 10rpx;
}

.scene-actions {
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 10rpx;
  margin-left: 20rpx;
}

.edit-btn {
  background-color: #f0f0f0;
  color: #333;
  font-size: 24rpx;
}

.delete-btn {
  font-size: 24rpx;
}

.empty-tip {
  text-align: center;
  padding: 200rpx 0;
  color: #999;
  font-size: 28rpx;
}