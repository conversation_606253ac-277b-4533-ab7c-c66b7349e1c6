// 场景数据模型
class Scene {
  constructor(data = {}) {
    this._id = data._id || null;
    this.name = data.name || '';
    this.description = data.description || '';
    this.prices = data.prices || {};
    this.images = data.images || [];
    this.createTime = data.createTime || null;
    this.updateTime = data.updateTime || null;
  }

  // 转换为数据库存储格式
  toDbObject(db) {
    const obj = {
      name: this.name,
      description: this.description,
      prices: this.prices,
      images: this.images
    };

    // 添加服务器时间
    if (!this.createTime) {
      obj.createTime = db.serverDate();
    }
    obj.updateTime = db.serverDate();

    return obj;
  }

  // 转换为API响应格式
  toApiResponse() {
    return {
      _id: this._id,
      name: this.name,
      description: this.description,
      prices: this.prices,
      images: this.images,
      createTime: this.createTime,
      updateTime: this.updateTime
    };
  }

  // 验证场景数据
  validate() {
    const errors = [];

    if (!this.name || this.name.trim().length === 0) {
      errors.push('场景名称不能为空');
    }

    if (this.name.length > 100) {
      errors.push('场景名称不能超过100个字符');
    }

    if (this.description.length > 1000) {
      errors.push('场景描述不能超过1000个字符');
    }

    if (this.images && !Array.isArray(this.images)) {
      errors.push('场景图片必须是数组格式');
    }

    return errors;
  }
}

module.exports = Scene;