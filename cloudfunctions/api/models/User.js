// 用户数据模型
class User {
  constructor(data = {}) {
    this._openid = data._openid || null;
    this.nickname = data.nickname || '新用户';
    this.avatarUrl = data.avatarUrl || '';
    this.isAdmin = data.isAdmin || false;
    this.createTime = data.createTime || null;
    this.updateTime = data.updateTime || null;
  }

  // 转换为数据库存储格式
  toDbObject(db) {
    const obj = {
      _openid: this._openid,
      nickname: this.nickname,
      avatarUrl: this.avatarUrl,
      isAdmin: this.isAdmin
    };

    // 添加服务器时间
    if (!this.createTime) {
      obj.createTime = db.serverDate();
    }
    obj.updateTime = db.serverDate();

    return obj;
  }

  // 转换为API响应格式
  toApiResponse() {
    return {
      openid: this._openid,
      nickname: this.nickname,
      avatarUrl: this.avatarUrl,
      isAdmin: this.isAdmin,
      hasLogin: true
    };
  }

  // 验证用户数据
  validate() {
    const errors = [];

    if (!this._openid) {
      errors.push('用户OpenID不能为空');
    }

    if (!this.nickname || this.nickname.trim().length === 0) {
      errors.push('用户昵称不能为空');
    }

    if (this.nickname.length > 50) {
      errors.push('用户昵称不能超过50个字符');
    }

    return errors;
  }
}

module.exports = User;