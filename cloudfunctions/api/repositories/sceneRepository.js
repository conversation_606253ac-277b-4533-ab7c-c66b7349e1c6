const BaseRepository = require('./baseRepository');
const { COLLECTIONS } = require('../config/database');

// 场景数据访问层
class SceneRepository extends BaseRepository {
  constructor() {
    super(COLLECTIONS.SCENES);
  }

  // 分页获取场景列表
  async getScenesList(page = 1, pageSize = 10) {
    const logger = require('../middlewares/logger');
    
    try {
      logger.info('数据库查询开始', { page, pageSize });
      
      const skip = (page - 1) * pageSize;
      
      // 获取场景列表
      logger.info('执行find查询', { skip, pageSize });
      const scenes = await this.find({}, {
        orderBy: { field: 'createTime', direction: 'desc' },
        skip,
        limit: pageSize
      });
      
      logger.info('find查询结果', { 
        scenesLength: scenes ? scenes.length : 0,
        scenesType: typeof scenes,
        firstScene: scenes && scenes.length > 0 ? scenes[0] : null
      });

      // 获取总数
      logger.info('执行count查询');
      const total = await this.count();
      logger.info('count查询结果', { total, totalType: typeof total });

      const result = {
        scenes: scenes || [],
        total: total || 0,
        page,
        pageSize,
        totalPages: Math.ceil((total || 0) / pageSize)
      };
      
      logger.info('数据库查询最终结果', result);
      return result;
    } catch (error) {
      logger.error('数据库查询失败', { 
        error: error.message,
        stack: error.stack
      });
      throw new Error(`获取场景列表失败: ${error.message}`);
    }
  }

  // 创建场景
  async createScene(sceneData) {
    try {
      const data = {
        name: sceneData.name,
        description: sceneData.description || '',
        prices: sceneData.prices || {},
        images: sceneData.images || [],
        createTime: this.db.serverDate(),
        updateTime: this.db.serverDate()
      };

      const result = await this.create(data);
      return result;
    } catch (error) {
      throw new Error(`创建场景失败: ${error.message}`);
    }
  }

  // 根据名称搜索场景
  async searchByName(name) {
    try {
      // 注意：微信云数据库不支持模糊查询，这里只能做精确匹配
      // 实际项目中可能需要使用云函数的数据库聚合查询功能
      return await this.find({ name });
    } catch (error) {
      throw new Error(`搜索场景失败: ${error.message}`);
    }
  }

  // 获取热门场景（按创建时间倒序）
  async getPopularScenes(limit = 10) {
    try {
      return await this.find({}, {
        orderBy: { field: 'createTime', direction: 'desc' },
        limit
      });
    } catch (error) {
      throw new Error(`获取热门场景失败: ${error.message}`);
    }
  }

  // 更新场景信息
  async updateScene(sceneId, updateData) {
    try {
      const data = {
        ...updateData,
        updateTime: this.db.serverDate()
      };
      
      const result = await this.updateById(sceneId, data);
      return result;
    } catch (error) {
      throw new Error(`更新场景失败: ${error.message}`);
    }
  }
}

module.exports = SceneRepository;