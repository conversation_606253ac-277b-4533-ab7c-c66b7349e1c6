const { db } = require('../config/database');

// 基础数据访问层
class BaseRepository {
  constructor(collectionName) {
    this.collection = db.collection(collectionName);
    this.db = db;
  }

  // 创建记录
  async create(data) {
    try {
      const result = await this.collection.add({ data });
      return result;
    } catch (error) {
      throw new Error(`创建记录失败: ${error.message}`);
    }
  }

  // 根据ID查找记录
  async findById(id) {
    try {
      const result = await this.collection.doc(id).get();
      return result.data;
    } catch (error) {
      throw new Error(`查找记录失败: ${error.message}`);
    }
  }

  // 根据条件查找单个记录
  async findOne(where) {
    try {
      const result = await this.collection.where(where).limit(1).get();
      return result.data.length > 0 ? result.data[0] : null;
    } catch (error) {
      throw new Error(`查找记录失败: ${error.message}`);
    }
  }

  // 根据条件查找多个记录
  async find(where = {}, options = {}) {
    try {
      let query = this.collection.where(where);

      // 排序
      if (options.orderBy) {
        query = query.orderBy(options.orderBy.field, options.orderBy.direction || 'asc');
      }

      // 分页
      if (options.skip) {
        query = query.skip(options.skip);
      }
      if (options.limit) {
        query = query.limit(options.limit);
      }

      const result = await query.get();
      return result.data;
    } catch (error) {
      throw new Error(`查找记录失败: ${error.message}`);
    }
  }

  // 根据ID更新记录
  async updateById(id, data) {
    try {
      const result = await this.collection.doc(id).update({ data });
      return result;
    } catch (error) {
      throw new Error(`更新记录失败: ${error.message}`);
    }
  }

  // 根据条件更新记录
  async update(where, data) {
    try {
      const result = await this.collection.where(where).update({ data });
      return result;
    } catch (error) {
      throw new Error(`更新记录失败: ${error.message}`);
    }
  }

  // 根据ID删除记录
  async deleteById(id) {
    try {
      const result = await this.collection.doc(id).remove();
      return result;
    } catch (error) {
      throw new Error(`删除记录失败: ${error.message}`);
    }
  }

  // 根据条件删除记录
  async delete(where) {
    try {
      const result = await this.collection.where(where).remove();
      return result;
    } catch (error) {
      throw new Error(`删除记录失败: ${error.message}`);
    }
  }

  // 统计记录数量
  async count(where = {}) {
    try {
      const result = await this.collection.where(where).count();
      return result.total;
    } catch (error) {
      throw new Error(`统计记录失败: ${error.message}`);
    }
  }
}

module.exports = BaseRepository;