const BaseRepository = require('./baseRepository');
const { COLLECTIONS } = require('../config/database');

// 用户数据访问层
class UserRepository extends BaseRepository {
  constructor() {
    super(COLLECTIONS.USERS);
  }

  // 根据OpenID查找用户
  async findByOpenId(openid) {
    try {
      return await this.findOne({ _openid: openid });
    } catch (error) {
      throw new Error(`查找用户失败: ${error.message}`);
    }
  }

  // 检查用户是否为管理员
  async isAdmin(openid) {
    try {
      const user = await this.findByOpenId(openid);
      return user ? user.isAdmin === true : false;
    } catch (error) {
      throw new Error(`检查管理员权限失败: ${error.message}`);
    }
  }

  // 创建或更新用户
  async createOrUpdate(userData) {
    try {
      const existingUser = await this.findByOpenId(userData._openid);
      
      if (existingUser) {
        // 用户已存在，更新信息
        const updateData = {
          nickname: userData.nickname,
          avatarUrl: userData.avatarUrl,
          updateTime: this.db.serverDate()
        };
        
        await this.update({ _openid: userData._openid }, updateData);
        
        // 返回更新后的用户信息
        return await this.findByOpenId(userData._openid);
      } else {
        // 用户不存在，创建新用户
        const newUserData = {
          _openid: userData._openid,
          nickname: userData.nickname || '新用户',
          avatarUrl: userData.avatarUrl || '',
          isAdmin: false,
          createTime: this.db.serverDate(),
          updateTime: this.db.serverDate()
        };
        
        await this.create(newUserData);
        return newUserData;
      }
    } catch (error) {
      throw new Error(`创建或更新用户失败: ${error.message}`);
    }
  }

  // 更新用户信息
  async updateUser(openid, updateData) {
    try {
      const data = {
        ...updateData,
        updateTime: this.db.serverDate()
      };
      
      const result = await this.update({ _openid: openid }, data);
      
      if (result.stats.updated > 0) {
        return await this.findByOpenId(openid);
      } else {
        throw new Error('用户不存在');
      }
    } catch (error) {
      throw new Error(`更新用户信息失败: ${error.message}`);
    }
  }
}

module.exports = UserRepository;