const { cloud } = require('../config/database');
const ApiResponse = require('../utils/response');
const Helpers = require('../utils/helpers');

// 认证中间件
class AuthMiddleware {
  // 验证用户身份
  static async validateUser(event, context) {
    const wxContext = Helpers.getWXContext(cloud);
    
    if (!wxContext || !wxContext.OPENID) {
      throw new Error('用户身份验证失败');
    }

    return {
      openid: wxContext.OPENID,
      appid: wxContext.APPID,
      unionid: wxContext.UNIONID
    };
  }

  // 检查管理员权限
  static async checkAdminPermission(openid) {
    const { db, COLLECTIONS } = require('../config/database');
    
    try {
      const adminRes = await db.collection(COLLECTIONS.USERS)
        .where({ 
          _openid: openid, 
          isAdmin: true 
        })
        .get();

      return adminRes.data.length > 0;
    } catch (error) {
      throw new Error('权限检查失败: ' + error.message);
    }
  }

  // 包装需要认证的处理函数
  static requireAuth(handler) {
    return async (event, context) => {
      try {
        const userContext = await AuthMiddleware.validateUser(event, context);
        event.userContext = userContext;
        return await handler(event, context);
      } catch (error) {
        return ApiResponse.unauthorized(error.message);
      }
    };
  }

  // 包装需要管理员权限的处理函数
  static requireAdmin(handler) {
    return async (event, context) => {
      try {
        const userContext = await AuthMiddleware.validateUser(event, context);
        const isAdmin = await AuthMiddleware.checkAdminPermission(userContext.openid);
        
        if (!isAdmin) {
          return ApiResponse.error('需要管理员权限', 403);
        }

        event.userContext = userContext;
        return await handler(event, context);
      } catch (error) {
        return ApiResponse.unauthorized(error.message);
      }
    };
  }
}

module.exports = AuthMiddleware;