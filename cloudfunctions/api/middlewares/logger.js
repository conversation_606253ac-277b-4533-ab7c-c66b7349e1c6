// 兼容本地调试环境
let winston;
let isWinstonAvailable = false;

try {
  winston = require('winston');
  isWinstonAvailable = true;
} catch (e) {
  console.warn('Winston not available in local debug mode, using console fallback');
  isWinstonAvailable = false;
}

let environment;
try {
  environment = require('../config/environment');
} catch (e) {
  environment = { logLevel: 'info', cloudEnvId: 'local' };
}

// 创建控制台日志备用方案
const consoleLogger = {
  info: (message, meta) => {
    console.log(`[INFO] ${message}`, meta ? JSON.stringify(meta) : '');
  },
  error: (message, meta) => {
    console.error(`[ERROR] ${message}`, meta ? JSON.stringify(meta) : '');
  },
  warn: (message, meta) => {
    console.warn(`[WARN] ${message}`, meta ? JSON.stringify(meta) : '');
  },
  debug: (message, meta) => {
    console.debug(`[DEBUG] ${message}`, meta ? JSON.stringify(meta) : '');
  }
};

// 创建Winston日志实例（如果可用）
const logger = isWinstonAvailable ? winston.createLogger({
  level: environment.logLevel || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json(),
    winston.format.printf(({ timestamp, level, message, meta, stack }) => {
      const logObj = {
        timestamp,
        level: level.toUpperCase(),
        message,
        env: environment.cloudEnvId
      };
      
      if (meta) {
        logObj.meta = meta;
      }
      
      if (stack) {
        logObj.stack = stack;
      }
      
      return JSON.stringify(logObj);
    })
  ),
  transports: [
    new winston.transports.Console()
  ]
}) : consoleLogger;

// 扩展日志功能
const extendedLogger = {
  info: logger.info.bind(logger),
  error: logger.error.bind(logger),
  warn: logger.warn.bind(logger),
  debug: logger.debug.bind(logger),
  
  // 记录请求
  logRequest(event, context) {
    const requestInfo = {
      requestId: context?.requestId || 'unknown',
      functionName: context?.functionName || 'unknown',
      action: event?.action || 'unknown',
      userAgent: event?.headers?.['User-Agent'] || 'unknown',
      timestamp: new Date().toISOString()
    };
    
    logger.info('请求开始', requestInfo);
  },

  // 记录响应
  logResponse(response, duration) {
    const responseInfo = {
      code: response?.code || 'unknown',
      message: response?.message || 'unknown',
      duration: `${duration}ms`,
      timestamp: new Date().toISOString()
    };
    
    logger.info('请求完成', responseInfo);
  },

  // 记录业务操作
  logBusiness(action, details = {}) {
    const businessInfo = {
      action,
      details,
      timestamp: new Date().toISOString()
    };
    
    logger.info(`业务操作: ${action}`, businessInfo);
  },

  // 记录性能指标
  logPerformance(operation, duration, details = {}) {
    const performanceInfo = {
      operation,
      duration: `${duration}ms`,
      details,
      timestamp: new Date().toISOString()
    };
    
    logger.info(`性能监控: ${operation}`, performanceInfo);
  }
};

module.exports = extendedLogger;