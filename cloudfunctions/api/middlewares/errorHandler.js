const ApiResponse = require('../utils/response');
const logger = require('./logger');
const { ValidationError } = require('../utils/validation');

// 自定义业务错误类
class BusinessError extends Error {
  constructor(message, code = 500) {
    super(message);
    this.name = 'BusinessError';
    this.code = code;
  }
}

// 全局错误处理器
class ErrorHandler {
  // 处理错误
  static handle(error, event, context) {
    // 记录错误日志
    logger.error('函数执行错误', {
      error: error.message,
      stack: error.stack,
      action: event?.action,
      requestId: context?.requestId
    });

    // 根据错误类型返回相应响应
    if (error instanceof ValidationError) {
      return ApiResponse.validationError(error.message, error.details);
    }

    if (error instanceof BusinessError) {
      return ApiResponse.error(error.message, error.code);
    }

    // 数据库相关错误
    if (error.message.includes('collection') || error.message.includes('database')) {
      return ApiResponse.error('数据库操作失败', 500);
    }

    // 微信云开发相关错误
    if (error.message.includes('wx-server-sdk') || error.message.includes('cloud')) {
      return ApiResponse.error('云服务错误', 500);
    }

    // 网络超时错误
    if (error.code === 'TIMEOUT' || error.message.includes('timeout')) {
      return ApiResponse.error('请求超时', 408);
    }

    // 默认服务器内部错误
    return ApiResponse.error('服务器内部错误', 500);
  }

  // 包装异步函数，自动处理错误
  static wrapAsync(fn) {
    return async (event, context) => {
      try {
        return await fn(event, context);
      } catch (error) {
        return ErrorHandler.handle(error, event, context);
      }
    };
  }
}

module.exports = {
  ErrorHandler,
  BusinessError
};