const { STATUS_CODES, MESSAGES } = require('../config/constants');

class ApiResponse {
  // 成功响应
  static success(data = null, message = MESSAGES.SUCCESS) {
    return {
      code: STATUS_CODES.SUCCESS,
      message,
      data,
      timestamp: new Date().toISOString()
    };
  }

  // 错误响应
  static error(message = MESSAGES.ERROR, code = STATUS_CODES.ERROR, data = null) {
    return {
      code,
      message,
      data,
      timestamp: new Date().toISOString()
    };
  }

  // 验证错误响应
  static validationError(message = MESSAGES.VALIDATION_ERROR, details = null) {
    return {
      code: STATUS_CODES.VALIDATION_ERROR,
      message,
      data: details,
      timestamp: new Date().toISOString()
    };
  }

  // 未授权响应
  static unauthorized(message = MESSAGES.UNAUTHORIZED) {
    return {
      code: STATUS_CODES.UNAUTHORIZED,
      message,
      data: null,
      timestamp: new Date().toISOString()
    };
  }

  // 用户未注册响应
  static userNotRegistered(data = null) {
    return {
      code: STATUS_CODES.USER_NOT_REGISTERED,
      message: MESSAGES.USER_NOT_REGISTERED,
      data,
      timestamp: new Date().toISOString()
    };
  }
}

module.exports = ApiResponse;