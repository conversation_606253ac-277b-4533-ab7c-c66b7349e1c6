// 工具函数集合
class Helpers {
  // 获取微信用户上下文
  static getWXContext(cloud) {
    try {
      return cloud.getWXContext();
    } catch (error) {
      console.error('获取微信用户上下文失败:', error);
      return null;
    }
  }

  // 生成分页参数
  static getPaginationParams(page = 1, pageSize = 10) {
    const pageNum = Math.max(1, parseInt(page) || 1);
    const size = Math.min(100, Math.max(1, parseInt(pageSize) || 10));
    const skip = (pageNum - 1) * size;
    
    return {
      page: pageNum,
      pageSize: size,
      skip
    };
  }

  // 深度克隆对象
  static deepClone(obj) {
    if (obj === null || typeof obj !== 'object') return obj;
    if (obj instanceof Date) return new Date(obj);
    if (obj instanceof Array) return obj.map(item => this.deepClone(item));
    if (typeof obj === 'object') {
      const clonedObj = {};
      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          clonedObj[key] = this.deepClone(obj[key]);
        }
      }
      return clonedObj;
    }
  }

  // 安全的JSON解析
  static safeJsonParse(str, defaultValue = null) {
    try {
      return JSON.parse(str);
    } catch (error) {
      return defaultValue;
    }
  }

  // 检查是否为空值
  static isEmpty(value) {
    return value === null || 
           value === undefined || 
           value === '' || 
           (Array.isArray(value) && value.length === 0) ||
           (typeof value === 'object' && Object.keys(value).length === 0);
  }

  // 格式化错误信息
  static formatError(error) {
    return {
      name: error.name || 'Error',
      message: error.message || '未知错误',
      stack: error.stack || null,
      timestamp: new Date().toISOString()
    };
  }
}

module.exports = Helpers;