const joi = require('joi');

// 验证规则定义
const schemas = {
  // 用户登录验证
  login: joi.object({
    nickname: joi.string().min(1).max(50).optional(),
    avatarUrl: joi.string().uri().optional().allow('')
  }),

  // 用户更新验证
  updateUser: joi.object({
    nickname: joi.string().min(1).max(50).optional(),
    avatarUrl: joi.string().uri().optional().allow('')
  }),

  // 场景创建验证
  createScene: joi.object({
    name: joi.string().min(1).max(100).required(),
    description: joi.string().max(1000).optional().allow(''),
    prices: joi.object().optional(),
    images: joi.array().items(joi.string()).optional()
  }),

  // 场景查询验证
  getScenes: joi.object({
    page: joi.number().integer().min(1).optional(),
    pageSize: joi.number().integer().min(1).max(100).optional()
  })
};

// 验证器类
class Validator {
  // 验证数据
  static validate(schemaName, data) {
    const schema = schemas[schemaName];
    if (!schema) {
      throw new Error(`未找到验证规则: ${schemaName}`);
    }

    const { error, value } = schema.validate(data, {
      allowUnknown: false, // 不允许未知字段
      stripUnknown: true   // 删除未知字段
    });

    if (error) {
      throw new ValidationError(error.details[0].message, error.details);
    }

    return value;
  }
}

// 自定义验证错误类
class ValidationError extends Error {
  constructor(message, details) {
    super(message);
    this.name = 'ValidationError';
    this.details = details;
  }
}

module.exports = {
  Validator,
  ValidationError,
  schemas
};