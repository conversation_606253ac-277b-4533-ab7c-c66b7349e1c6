const AuthService = require('../services/authService');
const ApiResponse = require('../utils/response');
const AuthMiddleware = require('../middlewares/auth');
const { Validator } = require('../utils/validation');
const { MESSAGES } = require('../config/constants');

// 认证处理器
class AuthHandler {
  constructor() {
    this.authService = new AuthService();
  }

  // 用户登录/注册
  async login(event, context) {
    try {
      // 验证用户身份
      const userContext = await AuthMiddleware.validateUser(event, context);
      
      // 验证请求参数
      const validatedData = Validator.validate('login', event);

      // 执行登录逻辑
      const user = await this.authService.login(userContext.openid, validatedData);

      return ApiResponse.success(user, MESSAGES.LOGIN_SUCCESS);
    } catch (error) {
      throw error; // 由全局错误处理器处理
    }
  }

  // 获取用户信息
  async getUserInfo(event, context) {
    try {
      // 验证用户身份
      const userContext = await AuthMiddleware.validateUser(event, context);

      // 获取用户信息
      const user = await this.authService.getUserInfo(userContext.openid);

      if (!user) {
        return ApiResponse.userNotRegistered({
          openid: userContext.openid,
          isAdmin: false,
          hasLogin: false
        });
      }

      return ApiResponse.success({
        ...user,
        openid: userContext.openid,
        isAdmin: user.isAdmin || false
      }, '获取用户信息成功');
    } catch (error) {
      throw error;
    }
  }

  // 检查管理员权限
  async checkAdmin(event, context) {
    try {
      // 验证用户身份
      const userContext = await AuthMiddleware.validateUser(event, context);

      // 检查管理员权限
      const result = await this.authService.checkAdmin(userContext.openid);

      return ApiResponse.success(result, MESSAGES.ADMIN_CHECK_SUCCESS);
    } catch (error) {
      throw error;
    }
  }
}

module.exports = AuthHandler;