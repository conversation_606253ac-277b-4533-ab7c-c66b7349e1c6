const SceneService = require('../services/sceneService');
const ApiResponse = require('../utils/response');
const AuthMiddleware = require('../middlewares/auth');
const { Validator } = require('../utils/validation');
const { MESSAGES } = require('../config/constants');

// 场景处理器
class SceneHandler {
  constructor() {
    this.sceneService = new SceneService();
  }

  // 获取场景列表
  async getScenes(event, context) {
    const logger = require('../middlewares/logger');
    
    try {
      logger.info('开始获取场景列表', { event });

      // 验证请求参数
      const validatedData = Validator.validate('getScenes', event);
      logger.info('参数验证成功', { validatedData });

      // 获取场景列表
      const result = await this.sceneService.getScenes(
        validatedData.page, 
        validatedData.pageSize
      );
      
      logger.info('场景服务返回结果', { 
        scenesCount: result.scenes ? result.scenes.length : 0,
        total: result.total,
        hasScenes: !!result.scenes
      });

      // 兼容前端期望的格式
      const response = {
        ...ApiResponse.success(result, MESSAGES.SCENE_GET_SUCCESS),
        success: true,
        scenes: result.scenes || [],
        total: result.total || 0
      };
      
      logger.info('最终响应格式', { 
        responseKeys: Object.keys(response),
        success: response.success,
        scenesLength: response.scenes.length
      });

      return response;
    } catch (error) {
      logger.error('获取场景列表出错', { 
        error: error.message,
        stack: error.stack
      });
      
      // 返回错误但保持兼容格式
      return {
        code: 500,
        success: false,
        message: error.message,
        scenes: [],
        total: 0,
        error: error.message
      };
    }
  }

  // 创建场景（需要管理员权限）
  async addScene(event, context) {
    try {
      // 验证管理员权限
      const userContext = await AuthMiddleware.validateUser(event, context);
      const isAdmin = await AuthMiddleware.checkAdminPermission(userContext.openid);
      
      if (!isAdmin) {
        return ApiResponse.error('需要管理员权限', 403);
      }

      // 验证请求参数
      const validatedData = Validator.validate('createScene', event);

      // 创建场景
      const result = await this.sceneService.createScene(validatedData);

      // 兼容前端期望的格式
      return {
        ...ApiResponse.success(result, MESSAGES.SCENE_CREATE_SUCCESS),
        success: result.success,
        sceneId: result.sceneId
      };
    } catch (error) {
      throw error;
    }
  }

  // 获取场景详情
  async getSceneDetail(event, context) {
    try {
      const { sceneId } = event;
      
      if (!sceneId) {
        return ApiResponse.validationError('场景ID不能为空');
      }

      // 获取场景详情
      const scene = await this.sceneService.getSceneDetail(sceneId);

      // 兼容前端期望的格式
      return {
        ...ApiResponse.success(scene, '获取场景详情成功'),
        success: true,
        scene: scene
      };
    } catch (error) {
      throw error;
    }
  }

  // 更新场景（需要管理员权限）
  async updateScene(event, context) {
    try {
      // 验证管理员权限
      const userContext = await AuthMiddleware.validateUser(event, context);
      const isAdmin = await AuthMiddleware.checkAdminPermission(userContext.openid);
      
      if (!isAdmin) {
        return ApiResponse.error('需要管理员权限', 403);
      }

      const { sceneId, ...updateData } = event;

      if (!sceneId) {
        return ApiResponse.validationError('场景ID不能为空');
      }

      // 更新场景
      const result = await this.sceneService.updateScene(sceneId, updateData);

      // 兼容前端期望的格式
      return {
        ...ApiResponse.success(result, '场景更新成功'),
        success: result.success
      };
    } catch (error) {
      throw error;
    }
  }

  // 删除场景（需要管理员权限）
  async deleteScene(event, context) {
    try {
      // 验证管理员权限
      const userContext = await AuthMiddleware.validateUser(event, context);
      const isAdmin = await AuthMiddleware.checkAdminPermission(userContext.openid);
      
      if (!isAdmin) {
        return ApiResponse.error('需要管理员权限', 403);
      }

      const { sceneId } = event;

      if (!sceneId) {
        return ApiResponse.validationError('场景ID不能为空');
      }

      // 删除场景
      const result = await this.sceneService.deleteScene(sceneId);

      // 兼容前端期望的格式
      return {
        ...ApiResponse.success(result, '场景删除成功'),
        success: result.success
      };
    } catch (error) {
      throw error;
    }
  }

  // 搜索场景
  async searchScenes(event, context) {
    try {
      const { keyword } = event;

      // 搜索场景
      const scenes = await this.sceneService.searchScenes(keyword);

      return ApiResponse.success({ scenes }, '搜索场景成功');
    } catch (error) {
      throw error;
    }
  }

  // 获取热门场景
  async getPopularScenes(event, context) {
    try {
      const { limit } = event;

      // 获取热门场景
      const scenes = await this.sceneService.getPopularScenes(limit);

      return ApiResponse.success({ scenes }, '获取热门场景成功');
    } catch (error) {
      throw error;
    }
  }
}

module.exports = SceneHandler;