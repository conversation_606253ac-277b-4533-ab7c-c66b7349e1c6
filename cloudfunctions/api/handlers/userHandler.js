const UserService = require('../services/userService');
const ApiResponse = require('../utils/response');
const AuthMiddleware = require('../middlewares/auth');
const { Validator } = require('../utils/validation');
const { MESSAGES } = require('../config/constants');

// 用户处理器
class UserHandler {
  constructor() {
    this.userService = new UserService();
  }

  // 更新用户信息
  async updateUser(event, context) {
    try {
      // 验证用户身份
      const userContext = await AuthMiddleware.validateUser(event, context);
      
      // 验证请求参数
      const validatedData = Validator.validate('updateUser', event);

      // 更新用户信息
      const updatedUser = await this.userService.updateUser(userContext.openid, validatedData);

      return ApiResponse.success(updatedUser, MESSAGES.USER_UPDATE_SUCCESS);
    } catch (error) {
      throw error;
    }
  }

  // 获取用户详情
  async getUserDetail(event, context) {
    try {
      // 验证用户身份
      const userContext = await AuthMiddleware.validateUser(event, context);

      // 获取用户详情
      const userDetail = await this.userService.getUserDetail(userContext.openid);

      return ApiResponse.success(userDetail, '获取用户详情成功');
    } catch (error) {
      throw error;
    }
  }

  // 获取用户统计信息（管理员权限）
  async getUserStats(event, context) {
    try {
      // 验证管理员权限
      const userContext = await AuthMiddleware.validateUser(event, context);
      const isAdmin = await AuthMiddleware.checkAdminPermission(userContext.openid);
      
      if (!isAdmin) {
        return ApiResponse.error('需要管理员权限', 403);
      }

      // 获取用户统计
      const stats = await this.userService.getUserStats();

      return ApiResponse.success(stats, '获取用户统计成功');
    } catch (error) {
      throw error;
    }
  }
}

module.exports = UserHandler;