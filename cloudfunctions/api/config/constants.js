// 业务常量定义
module.exports = {
  // 响应状态码
  STATUS_CODES: {
    SUCCESS: 0,
    ERROR: 500,
    UNAUTHORIZED: 401,
    FORBIDDEN: 403,
    NOT_FOUND: 404,
    VALIDATION_ERROR: 400,
    USER_NOT_REGISTERED: 1
  },

  // 响应消息
  MESSAGES: {
    SUCCESS: '操作成功',
    ERROR: '操作失败',
    UNAUTHORIZED: '未授权访问',
    FORBIDDEN: '权限不足',
    NOT_FOUND: '资源不存在',
    VALIDATION_ERROR: '参数验证失败',
    USER_NOT_REGISTERED: '用户未注册',
    LOGIN_SUCCESS: '登录成功',
    LOGOUT_SUCCESS: '退出成功',
    ADMIN_CHECK_SUCCESS: '权限验证成功',
    USER_UPDATE_SUCCESS: '用户信息更新成功',
    SCENE_CREATE_SUCCESS: '场景创建成功',
    SCENE_GET_SUCCESS: '场景获取成功'
  },

  // 默认分页配置
  PAGINATION: {
    DEFAULT_PAGE: 1,
    DEFAULT_PAGE_SIZE: 10,
    MAX_PAGE_SIZE: 100
  }
};