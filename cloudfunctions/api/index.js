// 级云函数主入口
const { ErrorHandler } = require('./middlewares/errorHandler');
const logger = require('./middlewares/logger');
const AuthHandler = require('./handlers/authHandler');
const UserHandler = require('./handlers/userHandler');
const SceneHandler = require('./handlers/sceneHandler');

// 初始化处理器
const authHandler = new AuthHandler();
const userHandler = new UserHandler();
const sceneHandler = new SceneHandler();

// 路由映射
const routes = {
  // 认证相关
  'login': authHandler.login.bind(authHandler),
  'getuserinfo': authHandler.getUserInfo.bind(authHandler),
  'checkadmin': authHandler.checkAdmin.bind(authHandler),
  
  // 用户相关
  'updateuser': userHandler.updateUser.bind(userHandler),
  'getuserdetail': userHandler.getUserDetail.bind(userHandler),
  'getuserstats': userHandler.getUserStats.bind(userHandler),
  
  // 场景相关
  'getscenes': sceneHandler.getScenes.bind(sceneHandler),
  'addscene': sceneHandler.addScene.bind(sceneHandler),
  'getscenedetail': sceneHandler.getSceneDetail.bind(sceneHandler),
  'updatescene': sceneHandler.updateScene.bind(sceneHandler),
  'deletescene': sceneHandler.deleteScene.bind(sceneHandler),
  'searchscenes': sceneHandler.searchScenes.bind(sceneHandler),
  'getpopularscenes': sceneHandler.getPopularScenes.bind(sceneHandler)
};

// 云函数主入口
exports.main = ErrorHandler.wrapAsync(async (event, context) => {
  const startTime = Date.now();
  
  // 记录请求开始
  logger.logRequest(event, context);

  // 获取操作类型
  const action = event.action;
  
  if (!action) {
    logger.error('缺少action参数', { event });
    const response = {
      code: 400,
      message: '缺少action参数',
      data: null,
      timestamp: new Date().toISOString()
    };
    logger.logResponse(response, Date.now() - startTime);
    return response;
  }

  // 查找对应的处理器
  const handler = routes[action];
  
  if (!handler) {
    logger.error('不支持的操作类型', { action, supportedActions: Object.keys(routes) });
    const response = {
      code: 400,
      message: `不支持的操作类型: ${action}`,
      data: { supportedActions: Object.keys(routes) },
      timestamp: new Date().toISOString()
    };
    logger.logResponse(response, Date.now() - startTime);
    return response;
  }

  try {
    // 执行具体的处理逻辑
    logger.info('开始执行处理器', { action });
    const response = await handler(event, context);
    
    // 记录响应
    const duration = Date.now() - startTime;
    logger.logResponse(response, duration);
    
    return response;
  } catch (error) {
    // 错误已经被 ErrorHandler.wrapAsync 处理
    throw error;
  }
});

// 导出路由信息（用于文档生成）
exports.routes = Object.keys(routes);

// 导出健康检查
exports.health = () => {
  return {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    routes: Object.keys(routes),
    version: '1.0.0'
  };
};