const UserRepository = require('../repositories/userRepository');
const User = require('../models/User');
const { BusinessError } = require('../middlewares/errorHandler');
const logger = require('../middlewares/logger');

// 认证服务
class AuthService {
  constructor() {
    this.userRepository = new UserRepository();
  }

  // 用户登录/注册
  async login(openid, userInfo = {}) {
    const startTime = Date.now();
    
    try {
      logger.logBusiness('用户登录尝试', { 
        openid: openid.substring(0, 8) + '***', // 脱敏处理
        hasNickname: !!userInfo.nickname 
      });

      // 创建用户模型
      const userData = new User({
        _openid: openid,
        nickname: userInfo.nickname || '新用户',
        avatarUrl: userInfo.avatarUrl || ''
      });

      // 验证用户数据
      const validationErrors = userData.validate();
      if (validationErrors.length > 0) {
        logger.warn('用户数据验证失败', { errors: validationErrors });
        throw new BusinessError(validationErrors.join(', '), 400);
      }

      // 创建或更新用户
      const user = await this.userRepository.createOrUpdate(userData.toDbObject(this.userRepository.db));

      logger.logBusiness('用户登录成功', { 
        openid: openid.substring(0, 8) + '***',
        isNewUser: !user._id,
        nickname: user.nickname 
      });

      logger.logPerformance('用户登录', Date.now() - startTime);

      return user;
    } catch (error) {
      logger.error('用户登录失败', { 
        openid: openid.substring(0, 8) + '***',
        error: error.message,
        duration: Date.now() - startTime
      });

      if (error instanceof BusinessError) {
        throw error;
      }
      throw new BusinessError(`登录失败: ${error.message}`, 500);
    }
  }

  // 获取用户信息
  async getUserInfo(openid) {
    try {
      const user = await this.userRepository.findByOpenId(openid);
      
      if (!user) {
        return null; // 用户未注册
      }

      return user;
    } catch (error) {
      throw new BusinessError(`获取用户信息失败: ${error.message}`, 500);
    }
  }

  // 检查管理员权限
  async checkAdmin(openid) {
    try {
      const isAdmin = await this.userRepository.isAdmin(openid);
      return {
        isAdmin,
        message: isAdmin ? '是管理员' : '不是管理员'
      };
    } catch (error) {
      throw new BusinessError(`权限检查失败: ${error.message}`, 500);
    }
  }
}

module.exports = AuthService;