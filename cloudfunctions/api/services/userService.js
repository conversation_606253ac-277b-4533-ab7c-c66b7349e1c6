const UserRepository = require('../repositories/userRepository');
const User = require('../models/User');
const { BusinessError } = require('../middlewares/errorHandler');

// 用户服务
class UserService {
  constructor() {
    this.userRepository = new UserRepository();
  }

  // 更新用户信息
  async updateUser(openid, updateData) {
    try {
      // 验证更新数据
      if (updateData.nickname) {
        if (updateData.nickname.trim().length === 0) {
          throw new BusinessError('用户昵称不能为空', 400);
        }
        if (updateData.nickname.length > 50) {
          throw new BusinessError('用户昵称不能超过50个字符', 400);
        }
      }

      // 更新用户信息
      const updatedUser = await this.userRepository.updateUser(openid, updateData);
      
      if (!updatedUser) {
        throw new BusinessError('用户不存在', 404);
      }

      return updatedUser;
    } catch (error) {
      if (error instanceof BusinessError) {
        throw error;
      }
      throw new BusinessError(`更新用户信息失败: ${error.message}`, 500);
    }
  }

  // 获取用户详细信息
  async getUserDetail(openid) {
    try {
      const user = await this.userRepository.findByOpenId(openid);
      
      if (!user) {
        throw new BusinessError('用户不存在', 404);
      }

      // 转换为API响应格式
      const userModel = new User(user);
      return userModel.toApiResponse();
    } catch (error) {
      if (error instanceof BusinessError) {
        throw error;
      }
      throw new BusinessError(`获取用户详情失败: ${error.message}`, 500);
    }
  }

  // 设置管理员权限（仅供内部使用）
  async setAdminRole(openid, isAdmin = true) {
    try {
      const updatedUser = await this.userRepository.updateUser(openid, { isAdmin });
      return updatedUser;
    } catch (error) {
      throw new BusinessError(`设置管理员权限失败: ${error.message}`, 500);
    }
  }

  // 获取用户统计信息
  async getUserStats() {
    try {
      const totalUsers = await this.userRepository.count();
      const adminUsers = await this.userRepository.count({ isAdmin: true });
      
      return {
        totalUsers,
        adminUsers,
        regularUsers: totalUsers - adminUsers
      };
    } catch (error) {
      throw new BusinessError(`获取用户统计失败: ${error.message}`, 500);
    }
  }
}

module.exports = UserService;