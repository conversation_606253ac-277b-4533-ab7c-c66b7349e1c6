const SceneRepository = require('../repositories/sceneRepository');
const Scene = require('../models/Scene');
const { BusinessError } = require('../middlewares/errorHandler');
const Helpers = require('../utils/helpers');

// 场景服务
class SceneService {
  constructor() {
    this.sceneRepository = new SceneRepository();
  }

  // 获取场景列表
  async getScenes(page = 1, pageSize = 10) {
    try {
      // 验证分页参数
      const pagination = Helpers.getPaginationParams(page, pageSize);
      
      // 获取场景列表
      const result = await this.sceneRepository.getScenesList(
        pagination.page, 
        pagination.pageSize
      );

      // 转换为API响应格式
      const scenes = result.scenes.map(scene => {
        const sceneModel = new Scene(scene);
        return sceneModel.toApiResponse();
      });

      return {
        scenes,
        total: result.total,
        page: result.page,
        pageSize: result.pageSize,
        totalPages: result.totalPages
      };
    } catch (error) {
      throw new BusinessError(`获取场景列表失败: ${error.message}`, 500);
    }
  }

  // 创建场景
  async createScene(sceneData) {
    try {
      // 创建场景模型
      const sceneModel = new Scene(sceneData);

      // 验证场景数据
      const validationErrors = sceneModel.validate();
      if (validationErrors.length > 0) {
        throw new BusinessError(validationErrors.join(', '), 400);
      }

      // 创建场景
      const result = await this.sceneRepository.createScene(sceneModel.toDbObject(this.sceneRepository.db));

      return {
        success: true,
        sceneId: result._id
      };
    } catch (error) {
      if (error instanceof BusinessError) {
        throw error;
      }
      throw new BusinessError(`创建场景失败: ${error.message}`, 500);
    }
  }

  // 获取场景详情
  async getSceneDetail(sceneId) {
    try {
      const scene = await this.sceneRepository.findById(sceneId);
      
      if (!scene) {
        throw new BusinessError('场景不存在', 404);
      }

      const sceneModel = new Scene(scene);
      return sceneModel.toApiResponse();
    } catch (error) {
      if (error instanceof BusinessError) {
        throw error;
      }
      throw new BusinessError(`获取场景详情失败: ${error.message}`, 500);
    }
  }

  // 更新场景
  async updateScene(sceneId, updateData) {
    try {
      // 验证更新数据
      if (updateData.name && updateData.name.trim().length === 0) {
        throw new BusinessError('场景名称不能为空', 400);
      }

      // 更新场景
      const result = await this.sceneRepository.updateScene(sceneId, updateData);
      
      return {
        success: result.stats.updated > 0,
        message: result.stats.updated > 0 ? '更新成功' : '场景不存在'
      };
    } catch (error) {
      if (error instanceof BusinessError) {
        throw error;
      }
      throw new BusinessError(`更新场景失败: ${error.message}`, 500);
    }
  }

  // 删除场景
  async deleteScene(sceneId) {
    try {
      const result = await this.sceneRepository.deleteById(sceneId);
      
      return {
        success: result.stats.removed > 0,
        message: result.stats.removed > 0 ? '删除成功' : '场景不存在'
      };
    } catch (error) {
      throw new BusinessError(`删除场景失败: ${error.message}`, 500);
    }
  }

  // 搜索场景
  async searchScenes(keyword) {
    try {
      if (!keyword || keyword.trim().length === 0) {
        throw new BusinessError('搜索关键词不能为空', 400);
      }

      const scenes = await this.sceneRepository.searchByName(keyword.trim());
      
      return scenes.map(scene => {
        const sceneModel = new Scene(scene);
        return sceneModel.toApiResponse();
      });
    } catch (error) {
      if (error instanceof BusinessError) {
        throw error;
      }
      throw new BusinessError(`搜索场景失败: ${error.message}`, 500);
    }
  }

  // 获取热门场景
  async getPopularScenes(limit = 10) {
    try {
      const scenes = await this.sceneRepository.getPopularScenes(limit);
      
      return scenes.map(scene => {
        const sceneModel = new Scene(scene);
        return sceneModel.toApiResponse();
      });
    } catch (error) {
      throw new BusinessError(`获取热门场景失败: ${error.message}`, 500);
    }
  }
}

module.exports = SceneService;