css
/* app.wxss - 全局样式 */
/* 基础变量 */
:root {
  --primary-color: #FF3333; /* 红色主色调 */
  --secondary-color: #000000; /* 黑色辅助色 */
  --text-color: #FFFFFF; /* 白色文本 */
  --gray-color: #999999; /* 灰色文本 */
  --light-gray: #F5F5F5; /* 浅灰色背景 */
}

/* 全局样式 */
page {
  background-color: #000000;
  color: #FFFFFF;
  font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
  min-height: 100vh;
}

.container {
  padding: 20rpx;
}

/* 标题样式 */
.title {
  font-size: 48rpx;
  font-weight: bold;
  margin: 30rpx 0;
  color: var(--primary-color);
}

.subtitle {
  font-size: 36rpx;
  font-weight: 500;
  margin: 20rpx 0;
}

/* 按钮样式 */
.btn {
  padding: 18rpx 36rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
}

.btn-primary {
  background-color: var(--primary-color);
  color: #FFFFFF;
}

.btn-primary:hover {
  background-color: #CC0000;
}

.btn-secondary {
  background-color: transparent;
  border: 2rpx solid var(--primary-color);
  color: var(--primary-color);
}

.btn-secondary:hover {
  background-color: rgba(255, 51, 51, 0.1);
}

/* 卡片样式 */
.card {
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
  backdrop-filter: blur(10rpx);
  transition: all 0.3s;
}

.card:hover {
  transform: translateY(-4rpx);
  box-shadow: 0 10rpx 20rpx rgba(255, 51, 51, 0.1);
}

/* 输入框样式 */
.input {
  padding: 16rpx 20rpx;
  border-radius: 8rpx;
  background-color: rgba(255, 255, 255, 0.1);
  color: #FFFFFF;
  border: none;
  outline: none;
  width: 100%;
  box-sizing: border-box;
  margin-bottom: 20rpx;
}

/* 动画效果 */
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}