// api/front/scene.js

/**
 * 获取场景列表
 * @param {Object} options - 查询选项
 * @param {number} options.page - 页码
 * @param {number} options.pageSize - 每页数量
 * @returns {Promise<Object>} 场景列表数据
 */
const getSceneList = async (options = {}) => {
  try {
    console.log('开始获取场景列表');
    const { page = 1, pageSize = 10 } = options;
    console.log('调用getscenes云函数');
    const { result } = await wx.cloud.callFunction({
      name: 'api',
      data: {
        action: 'getscenes',
        page,
        pageSize
      }
    });
    console.log('云函数调用结果:', result);
    if (result.success) {
      return {
        success: true,
        data: result.scenes,
        total: result.total,
        hasMore: result.scenes.length < result.total
      };
    } else {
      return {
        success: false,
        error: result.error || '获取场景列表失败'
      };
    }
  } catch (error) {
    console.error('获取场景列表失败:', error);
    console.error('错误堆栈:', error.stack);
    return {
      success: false,
      error: error.message,
      stack: error.stack
    };
  }
};

/**
 * 获取场景详情
 * @param {string} sceneId - 场景ID
 * @returns {Promise<Object>} 场景详情
 */
const getSceneDetail = async (sceneId) => {
  try {
    const models = await getApp().globalData.getModels();
    const { data } = await models.scenes.detail({
      filter: {
        where: {
          _id: sceneId
        }
      }
    });
    return {
      success: true,
      data
    };
  } catch (error) {
    console.error('获取场景详情失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

module.exports = {
  getSceneList,
  getSceneDetail
};
