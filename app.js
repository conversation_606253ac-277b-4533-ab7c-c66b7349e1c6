// 引入云开发SDK
import { init } from '@cloudbase/wx-cloud-client-sdk/lib/wxCloudClientSDK.esm.js';

App({
  globalData: {
    userInfo: null,      // 存储用户信息
    isAdmin: false,      // 管理员标识
    hasLogin: false,     // 登录状态
    envId: 'cloud1-2gokpbk70403ca97',  // 云环境ID（保持硬编码，小程序端不能直接读取.env）
    client: null,        // 云开发客户端
    autoLogin: false,    // 是否自动跳转登录页
    testMode: false      // 测试模式：false=使用云函数
  },

  onLaunch() {
    // 初始化云开发环境
    // 腾讯云CloudBase初始化（不是微信原生云开发）
    try {
      if (typeof wx.cloud !== 'undefined') {
        wx.cloud.init({
          env: this.globalData.envId,
          traceUser: true,
        });
        console.log('腾讯云CloudBase初始化成功');
      } else {
        console.log('当前环境不支持wx.cloud，可能需要使用CloudBase Web SDK');
      }
    } catch (error) {
      console.error('云开发初始化失败:', error);
      wx.showToast({
        title: '云服务初始化失败',
        icon: 'none',
        duration: 3000
      });
      return;
    }

    // 初始化数据模型客户端
    this.globalData.getModels = async () => {
      if (!this.globalData.client) {
        // 确保wx.cloud已初始化
        if (!wx.cloud) {
          console.error('云开发未初始化');
          throw new Error('云开发未初始化');
        }
        try {
          this.globalData.client = init(wx.cloud);
        } catch (error) {
          console.error('数据模型客户端初始化失败:', error);
          throw error;
        }
      }
      return this.globalData.client.models;
    };

    // 延迟检查登录状态，确保云开发完全初始化
    setTimeout(() => {
      this.checkLoginStatus()
        .then(() => {
          console.log('应用初始化完成');
        })
        .catch(err => {
          console.error('初始化失败:', err);
        });
    }, 1000);
  },

  /**
   * 检查用户登录状态
   * 已登录则获取用户信息，未登录则跳转登录页
   */
  async checkLoginStatus() {
    try {
      // 检查云开发是否已初始化
      if (!wx.cloud) {
        console.error('云开发服务未初始化');
        this.globalData.hasLogin = false;
        this.globalData.isAdmin = false;
        return;
      }

      console.log('开始检查用户登录状态...');

      // 调用新的api云函数获取用户信息
      const result = await wx.cloud.callFunction({
        name: 'api',
        data: {
          action: 'getuserinfo'
        }
      });

      console.log('云函数调用结果:', result);

      if (result && result.result) {
        const { code, data, message } = result.result;
        
        if (code === 0) {
          // 用户已登录，更新全局数据
          this.globalData.userInfo = data;
          this.globalData.hasLogin = true;
          this.globalData.isAdmin = data.isAdmin || false;
          console.log('用户登录状态检查成功，用户信息:', data);
        } else if (code === 1) {
          // 用户未注册，但可以正常使用前台功能
          console.log('用户未注册:', message);
          this.globalData.hasLogin = false;
          this.globalData.isAdmin = false;
          this.globalData.userInfo = data; // 包含基本的openid信息
        } else {
          // 其他错误
          console.log('获取用户信息失败:', message);
          this.globalData.hasLogin = false;
          this.globalData.isAdmin = false;
        }
        
        // 根据配置决定是否跳转登录页
        if (!this.globalData.hasLogin && this.globalData.autoLogin) {
          wx.navigateTo({
            url: '/pages/manage/login/login'
          });
        }
      } else {
        console.error('云函数返回格式异常:', result);
        this.globalData.hasLogin = false;
        this.globalData.isAdmin = false;
      }
    } catch (err) {
      console.error('检查登录状态失败:', err);
      
      // 设置默认状态
      this.globalData.hasLogin = false;
      this.globalData.isAdmin = false;
      
      // 检查具体错误类型
      if (err.errMsg && err.errMsg.includes('cloud.callFunction:fail')) {
        if (err.errMsg.includes('system error')) {
          console.error('云函数系统错误，可能是函数未部署或环境配置问题');
          wx.showModal({
            title: '提示',
            content: '云函数服务异常，请检查函数部署状态',
            showCancel: false
          });
        } else {
          wx.showToast({
            title: '服务暂时不可用',
            icon: 'none',
            duration: 2000
          });
        }
      } else if (err.message && err.message.includes('network')) {
        wx.showToast({
          title: '网络连接失败',
          icon: 'none',
          duration: 2000
        });
      } else {
        wx.showToast({
          title: '初始化失败',
          icon: 'none',
          duration: 2000
        });
      }
    }
  }
});
    