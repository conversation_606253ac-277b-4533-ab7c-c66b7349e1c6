// 云函数测试脚本
// 使用前请确保已安装云开发CLI并登录
require('dotenv').config(); // 加载.env文件中的环境变量
const tcb = require('@cloudbase/node-sdk');
const fs = require('fs');
const path = require('path');

// 初始化云开发环境
async function initCloud() {
  try {
    // 优先从环境变量获取环境ID
    let envId = process.env.CLOUD_ENV_ID;

    // 如果环境变量中没有，再尝试从project.config.json获取
    if (!envId) {
      const projectConfig = require('./project.config.json');
      envId = projectConfig.env;
    }

    if (!envId) {
      console.error('未找到环境ID，请在.env文件中设置CLOUD_ENV_ID环境变量');
      return null;
    }

    const cloud = tcb.init({
      env: envId,
      // 从环境变量读取凭证，适配当前.env文件中的变量名称（首字母大写）
      secretId: process.env.SecretId || '',
      secretKey: process.env.SecretKey || ''
    });

    console.log(`已初始化云开发环境: ${envId}`);
    return cloud;
  } catch (error) {
    console.error('初始化云开发环境失败:', error);
    return null;
  }
}

// 测试单个云函数
async function testFunction(cloud, functionName, params = {}) {
  console.log(`\n===== 测试云函数: ${functionName} =====`);
  console.log(`参数:`, params);

  try {
    const result = await cloud.callFunction({
      name: functionName,
      data: params
    });

    console.log(`调用成功，结果:`, result);
    return {
      functionName,
      success: true,
      result
    };
  } catch (error) {
    console.error(`调用失败:`, error);
    return {
      functionName,
      success: false,
      error: error.message
    };
  }
}

// 测试所有云函数
async function testAllFunctions() {
  const cloud = await initCloud();
  if (!cloud) return;

  // 读取cloudfunctions目录下的所有云函数
  const cloudfunctionsDir = path.join(__dirname, 'cloudfunctions');
  const functionNames = fs.readdirSync(cloudfunctionsDir).filter(name => {
    const stats = fs.statSync(path.join(cloudfunctionsDir, name));
    return stats.isDirectory() && name !== '.gitkeep';
  });

  console.log(`找到 ${functionNames.length} 个云函数:`, functionNames);

  // 为不同云函数准备测试参数
  const functionParams = {
    getscenes: { page: 1, limit: 10 },
    getscene: { _id: 'test_id' }, // 替换为实际存在的ID
    getuserinfo: {},
    login: {},
    addscene: { title: '测试场景', description: '测试描述' },
    deletescene: { _id: 'test_id' }, // 替换为实际存在的ID
    updatescene: { _id: 'test_id', title: '更新测试' }, // 替换为实际存在的ID
    checkadmin: {},
    initadmin: {}
  };

  // 执行测试
  const results = [];
  for (const funcName of functionNames) {
    const params = functionParams[funcName] || {};
    const result = await testFunction(cloud, funcName, params);
    results.push(result);
  }

  // 输出汇总结果
  console.log('\n\n\n\n===== 测试汇总 =====');
  const successCount = results.filter(r => r.success).length;
  console.log(`总测试: ${results.length}, 成功: ${successCount}, 失败: ${results.length - successCount}`);

  // 输出失败的云函数
  const failedFunctions = results.filter(r => !r.success);
  if (failedFunctions.length > 0) {
    console.log('\n失败的云函数:');
    failedFunctions.forEach(func => {
      console.log(`- ${func.functionName}: ${func.error}`);
    });
  }

  // 测试数据库访问
  await testDatabaseAccess(cloud);
}

// 测试数据库访问
async function testDatabaseAccess(cloud) {
  console.log(`\n
===== 测试数据库访问 =====`);
  try {
    const db = cloud.database();
    // 测试所有可用集合
    const collections = ['users', 'bookings', 'scenes'];
    let allCollectionsExist = true;

    for (const collName of collections) {
      try {
        console.log(`\n尝试访问集合: ${collName}`);
        const collection = db.collection(collName);
        const result = await collection.limit(1).get();

        if (result.data.length === 0) {
          console.log(`集合 ${collName} 存在，但没有数据`);
        } else {
          console.log(`集合 ${collName} 查询成功，结果:`, result.data[0]);
        }
      } catch (collError) {
        allCollectionsExist = false;
        console.error(`访问集合 ${collName} 失败:`, collError);
        if (collError.code === 'DATABASE_COLLECTION_NOT_EXIST') {
          console.log(`提示: ${collName} 集合不存在，请在云开发控制台创建该集合`);
        }
      }
    }

    if (allCollectionsExist) {
      console.log('\n所有集合访问测试完成，云函数可以正常访问数据库');
    } else {
      console.log('\n部分集合访问失败，请查看上面的错误信息');
    }
  } catch (error) {
    console.error('数据库访问测试发生错误:', error);
    console.log('云函数无法访问数据库，请检查权限设置');
  }
}

// 执行测试
testAllFunctions().catch(error => {
  console.error('测试过程发生错误:', error);
});