{"name": "@cloudbase/wx-cloud-client-sdk", "version": "1.6.1", "publishConfig": {"access": "public"}, "description": "wx cloud client sdk", "main": "lib/wxCloudClientSDK.cjs.js", "module": "lib/wxCloudClientSDK.esm.js", "browser": "lib/wxCloudClientSDK.umd.js", "types": "lib/index.d.ts", "files": ["lib"], "scripts": {"clean": "npx rimraf lib/*", "build": "npm run clean && rollup -c", "test": "jest", "test:coverage": "jest --coverage", "develop": "parcel demo/index.html", "sync-dts": "cd demo && tcb model sync-dts --envId=lowcode-4gs26nnz095f6f4d", "build-demo": "npx rimraf dist/* && parcel build demo/index.html --public-url ./", "publish": "npm publish --access public", "publish-demo": "tcb hosting deploy dist wx-cloud-client-sdk-demo -e lowcode-4gs26nnz095f6f4d", "docs": "typedoc --options typedoc.json", "deploy": "node ./scripts/deploy.mjs"}, "devDependencies": {"@cloudbase/adapter-interface": "^0.5.0", "@cloudbase/js-sdk": "2.15.0", "@rollup/plugin-commonjs": "^25.0.8", "@rollup/plugin-node-resolve": "^15.2.3", "@rollup/plugin-replace": "^5.0.7", "@rollup/plugin-typescript": "^11.1.6", "@tcwd/dev-tools": "^1.0.1", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "node-fetch": "2", "parcel-bundler": "1.6.1", "rollup": "^4.18.0", "ts-jest": "^29.2.2", "typedoc": "^0.25.13", "typedoc-plugin-markdown": "^4.0.3", "typescript": "4.4.4"}, "resolutions": {"@babel/preset-env": "7.13.8"}, "keywords": ["typescript", "javascript"]}